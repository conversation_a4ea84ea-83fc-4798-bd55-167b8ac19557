const nodemailer = require('nodemailer');
const path = require('node:path');
const hbs = require('nodemailer-express-handlebars');
const {
	MAIL_SERVICE,
	MAIL_FROM_EMAIL,
	MAIL_FROM_PASS,
	MAIL_FROM_NAME,
	MAIL_HOST,
	MAIL_PORT,
	MAIL_SECURE,
	MAIL_THEME
} = require('../Config/constants');

const sendEmail = async (
	res,
	email,
	subject,
	body,
	attachments = null,
	sendToMultiple = false
) => {
	// transport options
	const transportOptions = {
		service: MAIL_SERVICE,
		host: MAIL_HOST,
		port: MAIL_PORT,
		secure: MAIL_SECURE,
		auth: {
			user: MAIL_FROM_EMAIL,
			pass: MAIL_FROM_PASS
		}
	};
	// create transporter
	const transporter = nodemailer.createTransport(transportOptions);
	// handlebar options
	const handlebarOptions = {
		viewEngine: {
			extName: '.handlebars',
			partialsDir: path.resolve('./Views'),
			defaultLayout: false
		},
		viewPath: path.resolve('./Views'),
		extName: '.handlebars'
	};
	transporter.use('compile', hbs(handlebarOptions));
	// mail options
	const mailOptions = {
		from: {
			name: MAIL_FROM_NAME,
			address: MAIL_FROM_EMAIL
		},
		subject: subject,
		template: 'email',
		context: {
			appLogoUrl: MAIL_THEME.appLogoUrl,
			appName: MAIL_THEME.appName,
			primaryColor: MAIL_THEME.primaryColor,
			footerColor: MAIL_THEME.footerColor,
			footerTextColor: MAIL_THEME.footerTextColor,
			body: body,
			year: new Date().getFullYear()
		}
	};
	if (sendToMultiple) {
		mailOptions.to = 'Undisclosed Recipients';
		mailOptions.bcc = email;
	} else {
		mailOptions.to = email;
	}
	if (attachments) {
		mailOptions.attachments = attachments;
	}
	// send mail
	return await transporter.sendMail(mailOptions);
};

module.exports = sendEmail;