const AWS = require('aws-sdk');
const fs = require('fs');
const os = require('os');
const {
	S3_ACCESS_KEY,
	S3_SECRET_KEY,
	S3_REGION,
	S3_BUCKET_NAME
} = require('../Config/constants');
const tmpdir = os.tmpdir();

// Set the AWS credentials and region
AWS.config.update({
	accessKeyId: S3_ACCESS_KEY,
	secretAccessKey: S3_SECRET_KEY,
	region: S3_REGION
});

// Create an S3 bucket instance
const s3bucket = new AWS.S3();

// function to upload file on s3 bucket
const s3Upload = async (folder, fileName, contentType) => {
	return new Promise((resolve, reject) => {
		// Define the parameters for upload
		const params = {
			Bucket: S3_BUCKET_NAME,
			Key: folder + '/' + fileName,
			Body: fs.createReadStream(tmpdir + '/' + fileName),
			ContentType: contentType
		};
		// Upload the file to S3 Bucket
		s3bucket.upload(params, (err, data) => {
			if (err) {
				console.error('Error uploading file on Amazon S3:', err);
				reject(err);
			} else {
				console.log(
					'File uploaded successfully on Amazon S3. Location:',
					data.Location
				);
				resolve(data.Location);
			}
		});
	});
};

// function to delete file on s3 bucket
const s3Delete = async (filePath) => {
	console.log(filePath);
	return new Promise((resolve, reject) => {
		// Define the parameters for the deletion
		const params = {
			Bucket: S3_BUCKET_NAME,
			Key: filePath
		};
		// Delete the file from S3 Bucket
		s3bucket.deleteObject(params, (err, data) => {
			if (err) {
				console.error('Error deleting file on Amazon S3:', err);
				reject(err);
			} else {
				console.log('File deleted successfully on Amazon S3. Location:', data);
				resolve();
			}
		});
	});
};

module.exports = { s3Upload, s3Delete };
