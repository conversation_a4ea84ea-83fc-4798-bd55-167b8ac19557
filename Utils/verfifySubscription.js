const auth = require("./googleAuth");
const { google } = require("googleapis");

async function verifySubscription(packageName, subscriptionId, purchaseToken) {
  try {
    const authClient = await auth.getClient();
    const androidPublisher = google.androidpublisher({
      version: "v3",
      auth: authClient,
    });

    const res = await androidPublisher.purchases.subscriptions.get({
      packageName,
      subscriptionId,
      token: purchaseToken,
    });

    return res.data;
  } catch (err) {
    console.log("err: ", err);
    throw new Error(err);
  }
}

module.exports = {
  verifySubscription,
};
