const auth = require("./googleAuth");
const { google } = require("googleapis");
const axios = require("axios");
const jwt = require("jsonwebtoken");
const fs = require("fs");
const { ISSUER, BUNDEL_ID, KEY_ID, NODE_ENV } = require("../Config/constants");

async function verifySubscription(packageName, subscriptionId, purchaseToken) {
  try {
    const authClient = await auth.getClient();
    const androidPublisher = google.androidpublisher({
      version: "v3",
      auth: authClient,
    });

    const res = await androidPublisher.purchases.subscriptions.get({
      packageName,
      subscriptionId,
      token: purchaseToken,
    });

    return res.data;
  } catch (err) {
    throw new Error(err);
  }
}

const privateKey = fs.readFileSync("SubscriptionKey.p8");

function generateJWT() {
  const now = Math.floor(Date.now() / 1000);

  const payload = {
    iss: ISSUER,
    iat: now,
    exp: now + 3600, // 1 hour
    aud: "appstoreconnect-v1",
    bid: BUNDEL_ID,
  };

  const header = {
    alg: "ES256",
    kid: KEY_ID,
    typ: "JWT",
  };

  return jwt.sign(payload, privateKey, {
    algorithm: "ES256",
    header: header,
  });
}

async function getSubscriptionStatus(data) {
  const token = await generateJWT();
  console.log("token: ", token);

  const url =
    NODE_ENV !== "production"
      ? "https://api.storekit-sandbox.itunes.apple.com"
      : "https://api.storekit.itunes.apple.com";
  console.log("==============", url);
  const response = await axios.get(
    `${url}/inApps/v1/transactions/2000000982889377`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );

  return response.data;
}

async function decodeInfo(signedTransactionInfo) {
  try {
    // Decode JWT without verification (Apple's signature)
    const decoded = jwt.decode(signedTransactionInfo);
    return decoded;
  } catch (error) {
    console.error("Error decoding transaction info:", error);
    return null;
  }
}

function determineSubscriptionStatus(isActive, willRenew, expiresDate) {
  const now = Date.now();

  if (isActive) {
    return willRenew ? "active_will_renew" : "active_will_not_renew";
  } else {
    // Check if recently expired (within last 24 hours)
    const dayAgo = now - 24 * 60 * 60 * 1000;
    if (expiresDate > dayAgo) {
      return "recently_expired";
    }
    return "expired";
  }
}

async function parseSubscriptionStatus(statusResponse) {
  try {
    const { signedTransactionInfo } = statusResponse;

    if (!signedTransactionInfo || !signedTransactionInfo.length) {
      return {
        isValid: false,
        error: "No subscription data found",
      };
    }

    const transactionInfo = await decodeInfo(signedTransactionInfo);
    console.log("transactionInfo: ", transactionInfo);

    // Check if subscription is currently valid
    const now = Date.now();
    const expiresDate = transactionInfo.expiresDate;
    const isActive = expiresDate && expiresDate > now;

    return {
      isValid: isActive,
      isActive: isActive,
      productId: transactionInfo.productId,
      originalTransactionId: transactionInfo.originalTransactionId,
      transactionId: transactionInfo.transactionId,
      purchaseDate: new Date(transactionInfo.purchaseDate),
      expiresDate: new Date(expiresDate),
      environment: transactionInfo.environment,
      // status: determineSubscriptionStatus(isActive, willRenew, expiresDate),
      rawData: {
        transactionInfo,
      },
    };
  } catch (error) {
    console.error("Error parsing subscription status:", error);
    return {
      isValid: false,
      error: "Failed to parse subscription data",
    };
  }
}

async function validateAppleReceipt(receiptData) {
  try {
    const subscriptionStatus = await getSubscriptionStatus(receiptData);

    if (!subscriptionStatus) {
      throw new Error("Failed to get subscription status from Apple");
    }

    // Parse and validate the subscription data
    const validationResult = await parseSubscriptionStatus(subscriptionStatus);

    if (validationResult.isValid) {
      return validationResult;
    }

    return "";
  } catch (err) {
    return err;
  }
}
module.exports = {
  verifySubscription,
  validateAppleReceipt,
};
