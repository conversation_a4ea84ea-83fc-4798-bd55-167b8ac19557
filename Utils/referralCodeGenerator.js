const crypto = require("crypto");

/**
 * Generate a unique referral code
 * @param {string} firstName - User's first name
 * @param {string} lastName - User's last name
 * @param {number} userId - User's ID
 * @returns {string} - Generated referral code (6 chars: 3 alphabets + 3 numbers)
 */
function generateReferralCode(
  firstName,
  lastName,
  userId,
  timestamp = Date.now()
) {
  // Create a unique base string that includes userId and timestamp for uniqueness
  const baseString = `${firstName}${lastName}${userId}${timestamp}`;

  // Generate a hash for both alphabetic and numeric parts
  const hash = crypto.createHash("md5").update(baseString).digest("hex");

  // Generate 3 alphabetic characters from user's name with uniqueness
  const fullName = `${firstName}${lastName}`.toUpperCase();
  let alphabeticPart = "";

  // Extract only alphabetic characters from the name
  const nameLetters = fullName.replace(/[^A-Z]/g, "");

  if (nameLetters.length >= 3) {
    // Use first 3 letters from the name as base
    alphabeticPart = nameLetters.substring(0, 3);
  } else if (nameLetters.length > 0) {
    // If name has some letters but less than 3, use them and pad with hash-based letters
    alphabeticPart = nameLetters;
    const alphabets = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    // Fill remaining positions with hash-based letters for uniqueness
    for (let i = nameLetters.length; i < 3; i++) {
      const hashIndex = parseInt(hash.substring(i * 4, i * 4 + 2), 16);
      alphabeticPart += alphabets[hashIndex % alphabets.length];
    }
  } else {
    // If no letters in name, use hash-based generation
    const alphabets = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    for (let i = 0; i < 3; i++) {
      const hashIndex = parseInt(hash.substring(i * 4, i * 4 + 2), 16);
      alphabeticPart += alphabets[hashIndex % alphabets.length];
    }
  }

  // For users with same name, modify the alphabetic part slightly using userId
  if (nameLetters.length >= 3) {
    // Use userId to create variation in the alphabetic part for same names
    const userVariation = userId % 26; // 0-25
    const alphabets = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    // Replace the last character with a user-specific variation
    alphabeticPart = alphabeticPart.substring(0, 2) + alphabets[userVariation];
  }

  // Generate 3 numeric characters using hash
  let numericPart = "";
  for (let i = 0; i < 3; i++) {
    const hashIndex = parseInt(
      hash.substring((i + 6) * 2, (i + 6) * 2 + 2),
      16
    );
    numericPart += (hashIndex % 10).toString();
  }

  return `${alphabeticPart}${numericPart}`;
}

/**
 * Generate a simple random referral code as fallback
 * @returns {string} - Generated referral code (6 chars: 3 alphabets + 3 numbers)
 */
function generateSimpleReferralCode() {
  const alphabets = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const numbers = "0123456789";

  let result = "";

  // Generate 3 alphabetic characters
  for (let i = 0; i < 3; i++) {
    result += alphabets.charAt(Math.floor(Math.random() * alphabets.length));
  }

  // Generate 3 numeric characters
  for (let i = 0; i < 3; i++) {
    result += numbers.charAt(Math.floor(Math.random() * numbers.length));
  }

  return result;
}

module.exports = {
  generateReferralCode,
  generateSimpleReferralCode,
};
