const { JW<PERSON><PERSON><PERSON> } = require("../Config/constants");
const jwt = require('jsonwebtoken');
/* 
GENERATE RANDOM NUMBER FROM SPECIFIED RANGE
*/
function generateRandom(min, max) {
    return Math.floor(Math.random() * (max - min)) + min
}

/* 
CHECK IF OBJECT IS EMPTY
*/
function isEmpty(obj) {
    for (var key in obj) {
        if (obj.hasOwnProperty(key))
            return false;
    }
    return true;
}

/* 
CHECK IF TWO ARRAYS ARE EQUAL
*/
function arraysEqual(a, b) {
    if (a === b) return true;
    if (a == null || b == null) return false;
    if (a.length != b.length) return false;

    for (var i = 0; i < a.length; ++i)  if (a[i] !== b[i]) return false;

    return true;
}

// Function to generate random string
const generateRandomString = (length = 8) => {
    var result = '';
    var characters =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
};

const generateOTP = () => {
    return Math.floor(1000 + Math.random() * 9000);
};

const getBreathWorkProgressDataDayWise = (progressData) => {
    const convertTimeToSeconds = (time) => {
        const [hours, minutes, seconds] = time.split(':').map(Number);
        return (hours * 3600) + (minutes * 60) + seconds;
    };

    const convertSecondsToTime = (totalSeconds) => {
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    };

    // Group data by date and calculate total duration per date
    const groupedByDate = {};
    let overallDurationInSeconds = 0; // To accumulate the total duration for all dates

    progressData.forEach(record => {
        const date = record.createdAt.toISOString().split('T')[0]; // Extract the date (YYYY-MM-DD)
        const durationInSeconds = convertTimeToSeconds(record.duration);

        // Initialize or accumulate the total duration for the date
        if (!groupedByDate[date]) {
            groupedByDate[date] = 0;
        }
        groupedByDate[date] += durationInSeconds;

        // Accumulate the total duration for overall sum
        overallDurationInSeconds += durationInSeconds;
    });

    // Convert the total duration back to HH:MM:SS format for each date
    const result = Object.entries(groupedByDate).map(([date, totalSeconds]) => ({
        date,
        totalDuration: convertSecondsToTime(totalSeconds)
    }));

    // Add the overall duration as a key
    const overallDuration = convertSecondsToTime(overallDurationInSeconds);

    return {
        overallDuration,
        dataByDate: result
    };
};

const generateExpirationToken = (email) => {
    const date = new Date();

    date.setHours(date.getHours() + 2);
    let token = jwt.sign(
        { email: email, expiration: date },
        JWTTOKEN.secret,
        { expiresIn: JWTTOKEN.expiresIn }
    );

    return token;
};

module.exports = {
    generateRandom,
    isEmpty,
    arraysEqual,
    generateRandomString,
    generateOTP,
    getBreathWorkProgressDataDayWise,
    generateExpirationToken
}