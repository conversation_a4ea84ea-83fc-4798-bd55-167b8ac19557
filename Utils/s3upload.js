const AWS = require("aws-sdk");
const { S3_ACCESS_KEY, S3_SECRET_KEY, S3_REGION, S3_BUCKET_NAME } = require("../Config/constants");

const s3 = new AWS.S3({
    accessKeyId: S3_ACCESS_KEY,
    secretAccessKey: S3_SECRET_KEY,
    region: S3_REGION,
});

const S3_BUCKET = S3_BUCKET_NAME;

const initiateMultipartUpload = async (fileName, mimetype) => {
    try {
        const params = {
            Bucket: S3_BUCKET,
            Key: fileName,
            ContentType: mimetype,
            ACL: 'public-read', // Optional: If you want public access
        };
        const data = await s3.createMultipartUpload(params).promise();
        return data.UploadId; // Return the UploadId to upload parts
    } catch (err) {
        console.error("Error initiating multipart upload: ", err);
        throw new Error("Failed to initiate multipart upload");
    }
};

const uploadPart = async (fileName, uploadId, partNumber, buffer) => {
    try {
        const params = {
            Bucket: S3_BUCKET,
            Key: fileName,
            PartNumber: partNumber,
            UploadId: uploadId,
            Body: buffer,
        };
        const data = await s3.uploadPart(params).promise();
        return { ETag: data.ETag, PartNumber: partNumber }; // Return part data
    } catch (err) {
        console.error("Error uploading part: ", err);
        throw new Error("Failed to upload part");
    }
};

const completeMultipartUpload = async (fileName, uploadId, parts) => {
    try {
        const params = {
            Bucket: S3_BUCKET,
            Key: fileName,
            UploadId: uploadId,
            MultipartUpload: {
                Parts: parts, // Array of part ETags and part numbers
            },
        };
        await s3.completeMultipartUpload(params).promise();
        return `https://${S3_BUCKET}.s3.amazonaws.com/${fileName}`;
    } catch (err) {
        console.error("Error completing multipart upload: ", err);
        throw new Error("Failed to complete multipart upload");
    }
};

module.exports = {
    initiateMultipartUpload,
    uploadPart,
    completeMultipartUpload,
};
