const express = require("express");
const body_parser = require("body-parser");
const logger = require("morgan");
const cors = require("cors");
const jwt = require("jsonwebtoken");

require("dotenv").config();

const SHOULD_RUN_ON_HTTP = process.env.SHOULD_RUN_ON_HTTP;
const http = SHOULD_RUN_ON_HTTP == "true" ? require("http") : require("https");

const app = express();
require("./Config/globals"); // GLOBAL SETTINGS FILES
const server = http.createServer(app);
const port = process.env.PORT;

app.use(logger("dev"));
require("./Config/database");
const AuthService = require("./Services/App/Auth.Service");
const { SUBSCRIPTION_STATUS } = require("./Config/constants");

// app.use(body_parser.json({ limit: '10mb', extended: true }));
// app.use(body_parser.urlencoded({ limit: '10mb', extended: true }));

const language = require("i18n");
language.configure({
  locales: ["en"],
  defaultLocale: "en",
  autoReload: true,
  directory: __dirname + "/Locales",
  queryParameter: "lang",
  objectNotation: true,
  syncFiles: true,
});

// ------------------------      GLOBAL MIDDLEWARES -------------------------
app.use(cors({ origin: "*" }));
app.use(body_parser.json()); // ALLOW APPLICATION JSON
app.use(body_parser.urlencoded({ extended: false })); // ALLOW URL ENCODED PARSER
app.use(express.static(__dirname + "/Assets")); // SERVE STATIC IMAGES FROM ASSETS FOLDER
app.use(language.init); // MULTILINGUAL SETUP

// ------------------------    RESPONSE HANDLER    -------------------
app.use((req, res, next) => {
  const ResponseHandler = require("./Config/responseHandler");
  res.handler = new ResponseHandler(req, res);
  next();
});

const indexRouter = require("./Routes");
indexRouter(app);
require("./Utils/googleAuth");

// --------------------------    GLOBAL ERROR HANDLER    ------------------
app.use((err, req, res, next) => {
  if (res.headersSent) {
    return next(err);
  }
  console.log("err: ", err);
  res.handler.internalError(undefined, undefined, undefined, err);
});

server.listen(port, () => {
  console.log(`Server running at ${port}`);
});

function parsePubSubMessage(req, res, next) {
  try {
    const message = req.body.message;
    if (!message || !message.data) {
      throw new Error("Invalid Pub/Sub message format");
    }

    const jsonData = Buffer.from(message.data, "base64").toString("utf8");
    req.parsedData = JSON.parse(jsonData);
    next();
  } catch (err) {
    console.error("Error parsing message:", err);
    res.status(400).send("Bad Request");
  }
}

app.post("/play-subscription-notify", parsePubSubMessage, async (req, res) => {
  const notification = req.parsedData;

  // Example structure
  const { subscriptionNotification, packageName } = notification;

  if (subscriptionNotification) {
    const { notificationType, purchaseToken, subscriptionId } =
      subscriptionNotification;

    // Handle based on notificationType (e.g., 1 = RECOVERED, 2 = RENEWED, etc.)
    console.log("🔔 Subscription Event:", {
      notificationType,
      subscriptionId,
      purchaseToken,
    });

    // TODO: Validate token with Google API and update your DB
    if (notificationType === 1) {
      // RECOVERED
    } else if (notificationType === 2) {
      // RENEWED
    } else if (notificationType === 3) {
      const userData = await AuthService.getUserData({
        condition: { purchaseToken },
      });
      if (userData) {
        await AuthService.updateUserDetails({
          data: {
            purchaseToken: null,
            subscriptionStatus: SUBSCRIPTION_STATUS.CANCELLED,
          },
          condition: { id: userData.id },
        });
      }
    } else if (notificationType === 4) {
      // CANCELLED
    } else if (notificationType === 5) {
      // ON_HOLD
    } else if (notificationType === 6) {
      // PAUSED
    }
  }

  res.status(200).send("OK");
});

function certToPEM(cert) {
  const begin = "-----BEGIN CERTIFICATE-----\n";
  const end = "\n-----END CERTIFICATE-----";
  const formatted = cert.match(/.{1,64}/g).join("\n");
  return begin + formatted + end;
}

// Verify and decode Apple signed JWT
function verifyAndDecode(signedJWT) {
  const decoded = jwt.decode(signedJWT, { complete: true });
  if (!decoded) throw new Error("Invalid JWT");

  const x5c = decoded.header.x5c;
  if (!x5c || !x5c.length) throw new Error("No certificate found in JWT");

  const publicKeyPEM = certToPEM(x5c[0]);
  const verifiedPayload = jwt.verify(signedJWT, publicKeyPEM, {
    algorithms: ["ES256"],
  });

  return verifiedPayload;
}

app.post("/subscription/callback", async (req, res) => {
  try {
    const outerSignedPayload = req.body.signedPayload;
    const outerDecoded = jwt.decode(outerSignedPayload, { complete: true });
    const outerX5c = outerDecoded.header.x5c;

    const outerPublicKeyPEM = certToPEM(outerX5c[0]);

    try {
      const outerPayload = jwt.verify(outerSignedPayload, outerPublicKeyPEM, {
        algorithms: ["ES256"],
      });

      if (outerPayload.notificationType === "DID_CHANGE_RENEWAL_STATUS") {
        const renewalInfo = verifyAndDecode(
          outerPayload.data.signedRenewalInfo
        ); // your JWT verification here

        if (
          !renewalInfo.autoRenewStatus ||
          renewalInfo.autoRenewStatus === false
        ) {
          const transactionInfo = verifyAndDecode(
            outerPayload.data.signedTransactionInfo
          );
          console.log("User has cancelled auto-renewal");
          console.log(
            "Subscription will end on",
            new Date(transactionInfo.expiresDate)
          );

          const userData = await AuthService.getUserData({
            condition: { purchaseToken: transactionInfo.originalTransactionId },
          });
          if (userData) {
            await AuthService.updateUserDetails({
              data: {
                purchaseToken: null,
                subscriptionStatus: SUBSCRIPTION_STATUS.CANCELLED,
              },
              condition: { id: userData.id },
            });
          }
        } else {
          console.log("User re-enabled auto-renewal");
        }
      }

      if (outerPayload.notificationType === "EXPIRED") {
        const transactionInfo = verifyAndDecode(
          outerPayload.data.signedTransactionInfo
        );
        const userData = await AuthService.getUserData({
          condition: { purchaseToken: transactionInfo.originalTransactionId },
        });
        if (userData) {
          await AuthService.updateUserDetails({
            data: {
              purchaseToken: null,
              subscriptionStatus: SUBSCRIPTION_STATUS.CANCELLED,
            },
            condition: { id: userData.id },
          });
        }
        console.log("Subscription expired:", transactionInfo.productId);
        // ✅ Immediately set subscription to inactive
      }
    } catch (err) {
      console.error("Verification failed:", err);
    }
    res.status(200).send("OK");
  } catch (error) {
    console.error("Apple notification error:", error);
    res.status(400).send("Error");
  }
});
