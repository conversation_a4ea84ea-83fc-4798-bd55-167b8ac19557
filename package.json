{"name": "breathwork-backend", "version": "0.0.1", "description": "", "main": "server.js", "scripts": {"start": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"aws-sdk": "^2.1691.0", "bcrypt": "^5.1.1", "bluebird": "^3.7.2", "chalk": "^3.0.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^8.6.0", "express": "^4.17.1", "google-auth-library": "^8.9.0", "googleapis": "^118.0.0", "i18n": "^0.8.4", "iap-receipt-validator": "^1.0.6", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^2.0.2", "nodemailer": "^6.9.15", "nodemailer-express-handlebars": "^6.1.0", "nodemon": "^3.1.4", "sequelize": "^5.21.2", "tedious": "^6.6.2", "yup": "^1.4.0"}, "devDependencies": {"sequelize-cli": "^5.5.1"}}