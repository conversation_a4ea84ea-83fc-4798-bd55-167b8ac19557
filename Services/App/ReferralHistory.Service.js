const db = require("../../Database/Models");

module.exports = {
  // Get referral history for a user (people they referred)
  getUserReferrals: async (userId) => {
    const referrals = await db.userReferralHistory.findAll({
      where: {
        referrerUserId: userId,
        deletedAt: null,
      },
      include: [
        {
          model: db.user,
          as: "referred",
          attributes: ["id", "firstName", "lastName", "email"],
        },
      ],
      order: [["createdAt", "DESC"]],
    });

    return referrals;
  },

  // Get how a user was referred
  getUserReferredBy: async (userId) => {
    const referredBy = await db.userReferralHistory.findOne({
      where: {
        referredUserId: userId,
        deletedAt: null,
      },
      include: [
        {
          model: db.user,
          as: "referrer",
          attributes: ["id", "firstName", "lastName", "email", "referralCode"],
        },
      ],
    });

    return referredBy;
  },

  // Get referral statistics for a user
  getUserReferralStats: async (userId) => {
    const totalReferrals = await db.userReferralHistory.count({
      where: {
        referrerUserId: userId,
        deletedAt: null,
      },
    });

    const recentReferrals = await db.userReferralHistory.findAll({
      where: {
        referrerUserId: userId,
        deletedAt: null,
      },
      include: [
        {
          model: db.user,
          as: "referred",
          attributes: ["id", "firstName", "lastName", "email"],
        },
      ],
      order: [["createdAt", "DESC"]],
      limit: 5,
    });

    return {
      totalReferrals,
      recentReferrals,
    };
  },

  // Create referral history entry
  createReferralHistory: async (referrerUserId, referredUserId, referralCodeUsed) => {
    const referralHistory = await db.userReferralHistory.create({
      referrerUserId,
      referredUserId,
      referralCodeUsed,
    });

    return referralHistory;
  },
};
