const { Op } = require("sequelize");
const db = require("../../Database/Models");
const { startOfMonth, endOfMonth, format, parseISO } = require("date-fns");

module.exports = {
  getUserReferrals: async (userId, { month, year }) => {
    let whereCondition = {
      user_id: userId,
      deletedAt: null,
    };

    // Add month and year filtering based on the date field
    if (month && year) {
      const startDate = format(new Date(year, month - 1, 1), "yyyy-MM-dd");
      const endDate = format(
        endOfMonth(new Date(year, month - 1, 1)),
        "yyyy-MM-dd"
      );

      whereCondition = {
        ...whereCondition,
        date: {
          [Op.between]: [startDate, endDate],
        },
      };
    } else if (year) {
      const startDate = format(new Date(year, 0, 1), "yyyy-MM-dd");
      const endDate = format(new Date(year, 11, 31), "yyyy-MM-dd");

      whereCondition = {
        ...whereCondition,
        date: {
          [Op.between]: [startDate, endDate],
        },
      };
    }

    const referrals = await db.userReferral.findAll({
      where: whereCondition,
      attributes: ["id", "amount", "date", "image", "createdAt"],
      order: [["date", "DESC"]],
    });

    return referrals;
  },

  getUserReferralStats: async (userId, year) => {
    const currentYear = year || new Date().getFullYear();

    // Get total referrals and amount for the user
    const totalStats = await db.userReferral.findOne({
      attributes: [
        [db.sequelize.fn("COUNT", db.sequelize.col("id")), "totalReferrals"],
        [db.sequelize.fn("SUM", db.sequelize.col("amount")), "totalAmount"],
      ],
      where: {
        user_id: userId,
        deletedAt: null,
      },
    });

    // Get monthly stats for the specified year
    const monthlyStats = await db.userReferral.findAll({
      attributes: [
        [db.sequelize.fn("MONTH", db.sequelize.col("date")), "month"],
        [db.sequelize.fn("COUNT", db.sequelize.col("id")), "count"],
        [db.sequelize.fn("SUM", db.sequelize.col("amount")), "amount"],
      ],
      where: {
        user_id: userId,
        [Op.and]: [
          db.sequelize.where(
            db.sequelize.fn("YEAR", db.sequelize.col("date")),
            currentYear
          ),
          { deletedAt: null },
        ],
      },
      group: [db.sequelize.fn("MONTH", db.sequelize.col("date"))],
      order: [[db.sequelize.fn("MONTH", db.sequelize.col("date")), "ASC"]],
    });

    // Get current month stats
    const currentDate = new Date();
    const firstDayOfMonth = format(startOfMonth(currentDate), "yyyy-MM-dd");
    const lastDayOfMonth = format(endOfMonth(currentDate), "yyyy-MM-dd");

    const currentMonthStats = await db.userReferral.findOne({
      attributes: [
        [
          db.sequelize.fn("COUNT", db.sequelize.col("id")),
          "currentMonthReferrals",
        ],
        [
          db.sequelize.fn("SUM", db.sequelize.col("amount")),
          "currentMonthAmount",
        ],
      ],
      where: {
        user_id: userId,
        date: {
          [Op.between]: [firstDayOfMonth, lastDayOfMonth],
        },
        deletedAt: null,
      },
    });

    return {
      totalStats: {
        totalReferrals: totalStats?.dataValues?.totalReferrals || 0,
        totalAmount: totalStats?.dataValues?.totalAmount || 0,
      },
      currentMonthStats: {
        currentMonthReferrals:
          currentMonthStats?.dataValues?.currentMonthReferrals || 0,
        currentMonthAmount:
          currentMonthStats?.dataValues?.currentMonthAmount || 0,
      },
      monthlyStats: monthlyStats.map((stat) => ({
        month: stat.dataValues.month,
        count: stat.dataValues.count,
        amount: stat.dataValues.amount,
      })),
      year: currentYear,
    };
  },

  getLatestReferrals: async (userId, limit = 5) => {
    const referrals = await db.userReferral.findAll({
      where: {
        user_id: userId,
        deletedAt: null,
      },
      attributes: ["id", "amount", "date", "image", "createdAt"],
      order: [["date", "DESC"]],
      limit,
    });

    return referrals;
  },
};
