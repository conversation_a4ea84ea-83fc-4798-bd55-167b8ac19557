const UserModel = require("../../Database/Models").user;

module.exports = {
  getUserDetails: async (userId) => {
    const userDetails = await UserModel.findOne({
      where: {
        id: userId,
        deletedAt: null,
      },
    });

    if (userDetails) {
      return userDetails;
    }
    return false;
  },

  updateUserById: async (userId, payload) => {
    return await UserModel.update(payload, {
      where: { id: userId },
    });
  },

  updateAudioPreference: async (userId, audioPreference) => {
    return await UserModel.update(
      { audioPreference: audioPreference },
      {
        where: { id: userId },
      }
    );
  },

  updateMusicPreference: async (userId, musicPreference) => {
    return await UserModel.update(
      { musicPreference: musicPreference },
      {
        where: { id: userId },
      }
    );
  },

  updateAim: async (userId, aim) => {
    return await UserModel.update(
      { aim: aim },
      {
        where: { id: userId },
      }
    );
  },

  updateUpiId: async (userId, upiId) => {
    return await UserModel.update(
      { upiId: upiId },
      {
        where: { id: userId },
      }
    );
  },
};
