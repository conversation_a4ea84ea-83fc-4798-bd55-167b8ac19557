const breathWorkProgressModel = require("../../Database/Models").breathWorkProgress;

module.exports = {
    addProgress: async (duration, userId) => {
        const progressData = await breathWorkProgressModel.create({
            userId,
            duration
        })

        if (progressData) {
            return progressData
        }
        return false
    },

    listProgress: async (userId) => {
        const progressData = await breathWorkProgressModel.findAll({
            where: {
                userId,
                deletedAt: null
            },
            order: [['createdAt', 'DESC']]
        })

        if (progressData) {
            return progressData
        }
        return false
    }
}