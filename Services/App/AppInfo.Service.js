const db = require("../../Database/Models");

module.exports = {
  getActiveAppInfo: async () => {
    const appInfo = await db.appInfo.findOne({
      attributes: [
        "id",
        "iOSVersion",
        "androidVersion",
        "isForceUpdate",
        "isSoftUpdate",
        "updateMessage",
        "androidStoreUrl",
        "iOSStoreUrl",
        "isActive",
      ],
      order: [["createdAt", "DESC"]],
    });

    return appInfo;
  },

  getAllAppInfo: async () => {
    const appInfoList = await db.appInfo.findAll({
      attributes: [
        "id",
        "iOSVersion",
        "androidVersion",
        "isForceUpdate",
        "isSoftUpdate",
        "updateMessage",
        "androidStoreUrl",
        "iOSStoreUrl",
        "isActive",
        "createdAt",
        "updatedAt",
      ],
      order: [["createdAt", "DESC"]],
    });

    return appInfoList;
  },
};
