const { Op } = require("sequelize");

const CategoryModel = require("../../Database/Models").category;
const PlaylistModel = require("../../Database/Models").playlist;
const RecommendationModel = require("../../Database/Models").recommendation;
const RecommendationCateListModel =
  require("../../Database/Models").recommendationCateList;
const VideoActivityProgressModel =
  require("../../Database/Models").videoActivityProgress;

module.exports = {
  getCategoryData: async (name) => {
    // Fetch category details by name
    const categoryDetails = await CategoryModel.findOne({
      where: {
        name,
        deletedAt: null,
      },
      include: [
        {
          model: PlaylistModel,
          as: "playlists",
          attributes: { exclude: ["videoStatus"] },
        },
      ],
    });

    if (categoryDetails) {
      let responseData = {
        id: categoryDetails.id,
        name: categoryDetails.name,
        subTitle: categoryDetails.subTitle,
        description: categoryDetails.description,
        playlists: categoryDetails.playlists || [],
        bannerImage: categoryDetails.bannerImage,
        exerciseDurationTime: categoryDetails.exerciseDurationTime
          ? categoryDetails.exerciseDurationTime.split(",").map(Number)
          : [],
        guideline: categoryDetails.guideline,
        precaution: categoryDetails.precaution,
        holdTime: categoryDetails.holdTime,
        inhaleTime: categoryDetails.inhaleTime,
        exhaleTime: categoryDetails.exhaleTime,
        exhaleType: categoryDetails.exhaleType,
        inhaleType: categoryDetails.inhaleType,
      };

      // If category has subcategories, fetch them recursively and include in response
      if (categoryDetails.haveSubCategory) {
        const getCategoryHierarchy = async (parentId = null) => {
          const categories = await CategoryModel.findAll({
            where: {
              parentId,
              deletedAt: null,
            },
            include: [
              {
                model: PlaylistModel,
                as: "playlists",
                attributes: { exclude: ["videoStatus"] },
              },
            ],
          });

          const result = await Promise.all(
            categories.map(async (cat) => {
              const subcategories = await getCategoryHierarchy(cat.id);
              return {
                id: cat.id,
                name: cat.name,
                subTitle: cat.subTitle,
                description: cat.description,
                subcategories,
                playlists: cat.playlists || [],
                bannerImage: cat.bannerImage,
                exerciseDurationTime: cat.exerciseDurationTime
                  ? cat.exerciseDurationTime.split(",").map(Number)
                  : [],
                guideline: cat.guideline,
                precaution: cat.precaution,
                holdTime: cat.holdTime,
                inhaleTime: cat.inhaleTime,
                exhaleTime: cat.exhaleTime,
                exhaleType: cat.exhaleType,
                inhaleType: cat.inhaleType,
              };
            })
          );

          return result;
        };

        // Fetch subcategories if haveSubCategory is true
        responseData.subcategories = await getCategoryHierarchy(
          categoryDetails.id
        );
      }

      // Return category details (subcategories only included if haveSubCategory is true)
      return responseData;
    }

    return false;
  },

  getVideoData: async (id) => {
    const videoData = await PlaylistModel.findOne({
      where: {
        id,
        deletedAt: null,
      },
      attributes: { exclude: ["videoStatus"] },
    });

    if (videoData) return videoData;

    return false;
  },

  getAimListData: async () => {
    const aimListData = await RecommendationModel.findAll();

    if (aimListData && aimListData.length > 0) {
      return { data: aimListData };
    }
    return false;
  },

  getRecommendedCategories: async (id) => {
    // Fetch the recommendation list for the given category name
    const aimListData = await RecommendationCateListModel.findAll({
      where: {
        recommendationId: id,
      },
    });

    if (aimListData && aimListData.length > 0) {
      const formatted = [];
      for (let i = 0; i < aimListData.length; i++) {
        const item = aimListData[i];
        const [category, ...rest] = item.name.split("-");

        const subCategoryData = await CategoryModel.findOne({
          where: {
            name: rest.join("-").trim(),
          },
          include: [
            {
              model: PlaylistModel,
              as: "playlists",
            },
          ],
        });

        formatted.push({
          category: category.trim(),
          name: rest.join("-").trim(),
          type: item.type,
          subcategory: subCategoryData
            ? {
                ...JSON.parse(JSON.stringify(subCategoryData)),
                exerciseDurationTime: subCategoryData.exerciseDurationTime
                  ? subCategoryData.exerciseDurationTime.split(",").map(Number)
                  : [],
              }
            : {},
        });
      }
      return { data: formatted };
    }

    // Return false if no categories found
    return false;
  },

  getRecommendedCategoriesList: async () => {
    // Fetch the recommendation list for all aim categories
    const aimListData = await CategoryModel.findAll({
      where: {
        deletedAt: null,
        isAimCat: true,
      },
      attributes: ["recommendation"],
    });

    // Check if any recommendation data exists
    if (!aimListData || aimListData.length === 0) {
      console.log("No recommendation found for any aim categories.");
      return false;
    }

    // Collect all recommendations into one array (handle comma-separated IDs)
    const mappedCategories = aimListData
      .flatMap((item) =>
        item.recommendation ? item.recommendation.split(",") : []
      )
      .map((id) => id.trim())
      .filter((id) => !!id); // remove empty strings

    // If no valid IDs found, return false
    if (mappedCategories.length === 0) {
      console.log("No valid recommended category IDs found.");
      return false;
    }

    // Fetch the categories based on the recommendation list
    const categories = await CategoryModel.findAll({
      where: {
        id: {
          [Op.in]: mappedCategories,
        },
      },
      attributes: ["name"],
      include: [
        {
          model: CategoryModel,
          as: "parent",
          attributes: ["name"],
        },
      ],
    });

    // Return categories if found
    if (categories && categories.length > 0) return { data: categories };

    // If nothing is found
    return false;
  },

  addVideoActivity: async (userId, payload) => {
    const addedData = await VideoActivityProgressModel.create({
      userId,
      ...payload,
    });

    return addedData || null;
  },
};
