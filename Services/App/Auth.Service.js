const { Sequelize } = require("sequelize");
const { JWTTOKEN } = require("../../Config/constants");
const JwtToken = require("jsonwebtoken");
const { sequelize } = require("../../Database/Models");

const UserModel = require("../../Database/Models").user;
const UserTokenModel = require("../../Database/Models").userToken;
const UserReferralHistoryModel =
  require("../../Database/Models").userReferralHistory;
const breathWorkProgressModel =
  require("../../Database/Models").breathWorkProgress;
const RecommendationModel = require("../../Database/Models").recommendation;
const {
  generateReferralCode,
  generateSimpleReferralCode,
} = require("../../Utils/referralCodeGenerator");

module.exports = {
  Signup: async (req) => {
    const { email, password } = req?.body;

    const adminDetails = await AdminModel.findOne({
      where: { email },
    });

    if (adminDetails) {
      if (!adminDetails.validatePassword(password)) {
        return { error: "passwordNotMatch" }; //password not valid
      }

      if (!adminDetails.isActive) {
        return { error: "inActive" };
      }

      delete adminDetails.dataValues.password;
      const accessToken = await JwtToken.sign(
        {
          email: adminDetails.email,
          id: adminDetails.id,
        },
        JWTTOKEN.secret,
        { algorithm: JWTTOKEN.algorithm }
      );

      await AdminTokenModel.create({
        adminId: adminDetails.id,
        accessToken: accessToken,
      });
      adminDetails.dataValues.accessToken = accessToken;
      return {
        adminDetails: adminDetails,
        error: "",
      };
    } else {
      return { error: "userNotFound" }; //user doesn't exists
    }
  },

  getUserData: async ({ condition }) => {
    const userDetails = await UserModel.findOne({
      where: condition,
    });
    if (userDetails) {
      return userDetails;
    }
    return false;
  },

  getUserDetailsByCondition: async ({ condition }) => {
    const userDetails = await UserModel.findOne({
      where: condition,
      attributes: {
        exclude: ["password"],
      },
    });
    if (userDetails) {
      const accessToken = await JwtToken.sign(
        {
          email: userDetails.email,
          id: userDetails.id,
        },
        JWTTOKEN.secret,
        { algorithm: JWTTOKEN.algorithm }
      );

      if (!accessToken) {
        return { error: "tokenNotCreated" };
      }

      await UserTokenModel.create({
        userId: userDetails.id,
        accessToken: accessToken,
      });

      userDetails.dataValues.accessToken = accessToken;
      return userDetails;
    }
    return false;
  },

  validateReferralCode: async (referralCode) => {
    if (!referralCode) {
      return { isValid: true, referrerUser: null }; // No referral code provided, which is valid
    }

    const referrerUser = await UserModel.findOne({
      where: {
        referralCode: referralCode,
        deletedAt: null,
        status: true,
      },
    });

    if (!referrerUser) {
      return { isValid: false, referrerUser: null };
    }

    return { isValid: true, referrerUser };
  },

  register: async (payload) => {
    const transaction = await sequelize.transaction();

    try {
      // Create user first
      const createdUser = await UserModel.create(payload, { transaction });

      // Generate unique referral code for the new user
      let referralCode;
      let isUnique = false;
      let attempts = 0;
      const maxAttempts = 15;

      while (!isUnique && attempts < maxAttempts) {
        if (attempts < 10) {
          // Try the main generator with different timestamps for uniqueness
          const timestamp = Date.now() + attempts * 1000; // Add variation
          referralCode = generateReferralCode(
            createdUser.firstName,
            createdUser.lastName,
            createdUser.id,
            timestamp
          );
        } else {
          // Use simple random generator as fallback
          referralCode = generateSimpleReferralCode();
        }

        // Check if the generated code is unique
        const existingUser = await UserModel.findOne({
          where: { referralCode },
          transaction,
        });

        if (!existingUser) {
          isUnique = true;
        } else {
          attempts++;
        }
      }

      // If still not unique after all attempts, throw error
      if (!isUnique) {
        throw new Error(
          `Unable to generate unique referral code after ${maxAttempts} attempts`
        );
      }

      // Update user with referral code
      await createdUser.update({ referralCode }, { transaction });

      // If a referral code was used during signup, create referral history
      if (payload.referralCodeUsed && payload.referrerUserId) {
        await UserReferralHistoryModel.create(
          {
            referrerUserId: payload.referrerUserId,
            referredUserId: createdUser.id,
            referralCodeUsed: payload.referralCodeUsed,
          },
          { transaction }
        );
      }

      const accessToken = await JwtToken.sign(
        {
          email: createdUser.email,
          id: createdUser.id,
        },
        JWTTOKEN.secret,
        { algorithm: JWTTOKEN.algorithm }
      );

      if (!accessToken) {
        await transaction.rollback();
        return { error: "tokenNotCreated" };
      }

      await UserTokenModel.create(
        {
          userId: createdUser.id,
          accessToken: accessToken,
        },
        { transaction }
      );

      await transaction.commit();

      delete createdUser.dataValues.password;
      createdUser.dataValues.accessToken = accessToken;
      createdUser.dataValues.referralCode = referralCode;

      return createdUser;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  login: async (req) => {
    const { password } = req?.body;

    let condition = { email: req.body.email, deletedAt: null };

    const userDetails = await UserModel.findOne({
      where: condition,
      attributes: {
        exclude: ["resetPasswordOtp"],
      },
    });
    if (!userDetails) {
      return { error: "userNotFound" };
    }
    if (!userDetails.status) {
      return { error: "userInactive" }; // User is inactive
    }

    if (!userDetails.validatePassword(password)) {
      return { error: "passwordNotMatch" }; // Password does not match
    }

    const accessToken = await JwtToken.sign(
      {
        email: userDetails.email,
        id: userDetails.id,
      },
      JWTTOKEN.secret,
      { algorithm: JWTTOKEN.algorithm }
    );

    if (!accessToken) {
      return { error: "tokenNotCreated" };
    }

    await UserTokenModel.create({
      userId: userDetails.id,
      accessToken: accessToken,
    });

    delete userDetails.dataValues.password;

    userDetails.dataValues.accessToken = accessToken;

    return userDetails;
  },

  updateUserDetails: async ({ data, condition }) => {
    console.log("condition: ", condition);
    const update = await UserModel.update({ ...data }, { where: condition });
    if (update) {
      return update;
    }
    return false;
  },

  getUserDataById: async (userId) => {
    const userDetails = await UserModel.findOne({
      where: { id: userId },
    });

    if (userDetails) {
      return userDetails;
    }
    return false;
  },

  logout: async (userId) => {
    const userLogout = await UserTokenModel.destroy({
      where: { userId: userId },
    });
    return userLogout;
  },

  delete: async (userId) => {
    try {
      // Start a transaction
      const transaction = await sequelize.transaction();

      // Delete from UserModel
      await UserModel.destroy({ where: { id: userId }, transaction });

      // Delete related data from UserTokenModel
      await UserTokenModel.destroy({ where: { userId }, transaction });

      // Delete related data from breathWorkProgressModel
      await breathWorkProgressModel.destroy({ where: { userId }, transaction });

      // Commit the transaction
      await transaction.commit();

      return true;
    } catch (error) {
      console.error("Error deleting user and related data:", error);
      return false;

      // Rollback the transaction in case of an error
      await transaction.rollback();
    }
  },

  updateUser: async (userId, payload) => {
    const update = await UserModel.update(
      {
        firstName: payload.firstName,
        lastName: payload.lastName,
      },
      {
        where: {
          id: userId,
        },
      }
    );
    if (update) {
      return update;
    }
    return false;
  },
};
