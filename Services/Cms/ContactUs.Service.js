const Sequelize = require('sequelize');
const { PAGE_LIMIT } = require('../../Config/constants');
const ContactUsModel = require("../../Database/Models").contactUs;

module.exports = {
    getContactUsList: async (reqParams) => {
        const offset = (reqParams.page - 1) * PAGE_LIMIT;
        const limit = PAGE_LIMIT;
        let sortOrder =
            reqParams.sort_order === undefined ? 'desc' : reqParams.sort_order;
        let sortBy = reqParams.sort_by === undefined ? 'id' : reqParams.sort_by;
        let search = reqParams.search === undefined ? "" : reqParams.search;

        let whereCondition = [{ deletedAt: null }];
        if (search) {
            whereCondition.push({
                [Sequelize.Op.or]: [
                    { userName: { [Sequelize.Op.like]: `%${search}%` } },
                    { email: { [Sequelize.Op.like]: `%${search}%` } },
                ]
            });
        }


        const contactUsList = await ContactUsModel.findAndCountAll({
            where: whereCondition,
            order: [[Sequelize.literal(`${sortBy}`), `${sortOrder}`]],
            offset: offset,
            limit: limit
        })

        if (contactUsList) {
            return contactUsList;
        }

        return false
    },

    getContactUsDataById: async (id) => {
        const contactUsData = await ContactUsModel.findOne({
            where: {
                id: id,
                deletedAt: null
            },
        });

        if (contactUsData) {
            return contactUsData;
        }
        return false;
    },

    contactUsDelete: async (id) => {
        let deletedContactUs = await ContactUsModel.destroy({ where: { id: id } });

        if (deletedContactUs) {
            return deletedContactUs
        }
        return false
    }
}