const { JWTTOKEN } = require('../../Config/constants');
const JwtToken = require("jsonwebtoken");

const AdminModel = require('../../Database/Models').admin
const AdminTokenModel = require('../../Database/Models').adminToken

module.exports = {
	login: async (req) => {
		const { email, password } = req?.body;

		const adminDetails = await AdminModel.findOne({
			where: { email }
		});

		if (adminDetails) {
			if (!adminDetails.validatePassword(password)) {
				return { error: 'passwordNotMatch' }; //password not valid
			}

			if (!adminDetails.isActive) {
				return { error: 'inActive' };
			}

			delete adminDetails.dataValues.password;
			const accessToken = await JwtToken.sign(
				{
					email: adminDetails.email,
					id: adminDetails.id
				},
				JWTTOKEN.secret,
				{ algorithm: JWTTOKEN.algorithm }
			);

			await AdminTokenModel.create({
				adminId: adminDetails.id,
				accessToken: accessToken
			});
			adminDetails.dataValues.accessToken = accessToken;
			return {
				adminDetails: adminDetails,
				error: ''
			};
		} else {
			return { error: 'userNotFound' }; //user doesn't exists
		}
	},

	checkEmailExists: (email) => {
		return AdminModel.findOne({
			where: {
				email: email
			}
		}).then((data) => {
			if (data) {
				return data;
			} else {
				return false;
			}
		});
	},

	getAdminDataByCondition: async (condition) => {
		const adminData = await AdminModel.findOne({
			where: condition,
			attributes: {
				exclude: [
					'password',
				]
			}
		});
		if (adminData) {
			return adminData;
		}
		return false;
	},

	logout: async (id) => {
		const deleteData = await AdminTokenModel.destroy({
			where: { adminId: id }
		});
		return deleteData;
	},

	getAdminById: async (id) => {
		return AdminModel.findOne({
			where: {
				id
			}
		}).then((data) => {
			if (data) {
				return data;
			} else {
				return false;
			}
		});
	},

	update: async ({ data, condition }) => {
		const update = await AdminModel.update({ ...data }, { where: condition });

		if (update?.[0]) {
			const data = await AdminModel.findOne({
				where: condition,
				attributes: {
					exclude: ['password'],
				},
				include: [
					{
						model: AdminTokenModel,
						attributes: ['accessToken'],
					},
				],
			});

			data.dataValues.accessToken =
				data.adminTokens?.[0]?.accessToken
			delete data.dataValues.adminTokens;
			return data;
		}
		return false;
	},

	updateToken: async (id, token) => {
		const update = await AdminModel.update({ otp: token }, {
			where: {
				id: id,
				deletedAt: null
			}
		})

		if (update?.[0]) {
			return update
		}
		return false
	},

	getToken: async(token)=>{
		const tokenData= await AdminModel.findOne({
			otp:token
		})
		if(tokenData){
			return tokenData
		}
		return false
	},

	findAdmin:async(email,otpToken)=>{
		const adminData= await AdminModel.findOne({
			otp:otpToken,
			email:email
		})
		if(adminData){
			return adminData
		}
		return false
	}

}