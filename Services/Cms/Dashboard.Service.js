const { Op, fn, literal, col } = require("sequelize");
const { subDays, startOfWeek, format, parseISO, addDays } = require("date-fns");

const UserModel = require("../../Database/Models").user;
const VideoActivityModel =
  require("../../Database/Models").videoActivityProgress;
const BreathWorkModel = require("../../Database/Models").breathWorkProgress;

module.exports = {
  getTotalUser: async () => {
    return await UserModel.count({
      where: {
        deletedAt: null,
      },
    });
  },
  getUsersStat: async (type) => {
    const days = type === "week" ? 7 : 30;
    const fromDate = subDays(new Date(), days);

    if (type === "week") {
      // Group by date (last 7 days)
      const users = await UserModel.findAll({
        where: {
          deletedAt: null,
          createdAt: {
            [Op.gte]: fromDate,
          },
        },
        attributes: [
          [fn("DATE", col("createdAt")), "date"],
          [fn("COUNT", col("id")), "count"],
        ],
        group: [literal("DATE(createdAt)")],
        order: [[literal("DATE(createdAt)"), "ASC"]],
      });

      // Fill missing days with 0
      const result = {};
      for (let i = 0; i < 7; i++) {
        const date = format(addDays(fromDate, i), "yyyy-MM-dd");
        result[date] = 0;
      }

      users.forEach((entry) => {
        const date = format(new Date(entry.getDataValue("date")), "yyyy-MM-dd");
        result[date] = parseInt(entry.getDataValue("count"));
      });

      return Object.entries(result).map(([date, count]) => ({ date, count }));
    } else {
      // Group by week (last 30 days)
      const users = await UserModel.findAll({
        where: {
          deletedAt: null,
          createdAt: {
            [Op.gte]: fromDate,
          },
        },
        attributes: [
          [fn("YEAR", col("createdAt")), "year"],
          [fn("WEEK", col("createdAt"), 1), "week"],
          [fn("COUNT", col("id")), "count"],
        ],
        group: [literal("YEAR(createdAt)"), literal("WEEK(createdAt, 1)")],
        order: [
          [literal("YEAR(createdAt)"), "ASC"],
          [literal("WEEK(createdAt, 1)"), "ASC"],
        ],
      });

      // Map results by year-week
      const weekMap = {};
      users.forEach((entry) => {
        const year = entry.getDataValue("year");
        const week = entry.getDataValue("week");
        const key = `${year}-${week}`;
        weekMap[key] = parseInt(entry.getDataValue("count"));
      });

      // Generate 5 weeks with default 0
      const weeks = [];
      const start = startOfWeek(fromDate, { weekStartsOn: 1 });

      for (let i = 0; i < 5; i++) {
        const weekStart = addDays(start, i * 7);
        const year = parseInt(format(weekStart, "RRRR"));
        const week = parseInt(format(weekStart, "II"));
        const key = `${year}-${week}`;

        weeks.push({
          date: `Week ${i + 1}`,
          count: weekMap[key] || 0,
        });
      }

      return weeks;
    }
  },
  getTotalSubscribedUser: async () => {
    return await UserModel.count({
      where: {
        deletedAt: null,
        subscriptionStatus: "subscribed",
      },
    });
  },
  getBreathworkViews: async () => {
    return await VideoActivityModel.count({
      where: {
        type: "breathwork",
        isPlay: true,
      },
    });
  },
  getAthleticBreathworkViews: async () => {
    const data = await VideoActivityModel.count({
      where: {
        type: "athleticBreathwork",
        isPlay: true,
      },
    });
    return data;
  },
  getbreatworkProgressStat: async (type) => {
    const days = type === "week" ? 7 : 30;
    const fromDate = subDays(new Date(), days);

    let progress = [];
    if (type === "week") {
      // Group by date
      progress = await BreathWorkModel.findAll({
        where: {
          createdAt: {
            [Op.gte]: fromDate,
          },
        },
        attributes: [
          [fn("DATE", col("createdAt")), "date"],
          [fn("COUNT", col("id")), "count"],
        ],
        group: [literal("DATE(createdAt)")],
        order: [[literal("DATE(createdAt)"), "ASC"]],
      });

      return progress.map((entry) => ({
        date: format(new Date(entry.getDataValue("date")), "yyyy-MM-dd"),
        count: parseInt(entry.getDataValue("count")),
      }));
    } else {
      // For 30-day view → group by ISO week
      const rawData = await BreathWorkModel.findAll({
        where: {
          createdAt: {
            [Op.gte]: fromDate,
          },
        },
        attributes: [
          [fn("YEAR", col("createdAt")), "year"],
          [fn("WEEK", col("createdAt"), 1), "week"], // ISO week
          [fn("COUNT", col("id")), "count"],
        ],
        group: [literal("YEAR(createdAt)"), literal("WEEK(createdAt, 1)")],
        order: [
          [literal("YEAR(createdAt)"), "ASC"],
          [literal("WEEK(createdAt, 1)"), "ASC"],
        ],
      });

      // Map week data
      const weekMap = {};
      rawData.forEach((entry) => {
        const year = entry.getDataValue("year");
        const week = entry.getDataValue("week");
        const key = `${year}-${week}`;
        weekMap[key] = parseInt(entry.getDataValue("count"));
      });

      // Generate all week ranges in last 30 days
      const weeks = [];
      const now = new Date();
      const start = startOfWeek(fromDate, { weekStartsOn: 1 });

      for (let i = 0; i < 5; i++) {
        const weekStart = addDays(start, i * 7);
        const weekYear = parseInt(format(weekStart, "RRRR"));
        const weekNumber = parseInt(format(weekStart, "II"));

        const key = `${weekYear}-${weekNumber}`;
        const label = `Week ${i + 1}`;

        weeks.push({
          date: label,
          count: weekMap[key] || 0,
        });
      }

      return weeks;
    }
  },

  getSubscriptionStats: async (type = "week") => {
    const days = type === "week" ? 7 : 30;
    const fromDate = subDays(new Date(), days);

    let groupByClause, attributes;

    if (type === "week") {
      // Group by date
      groupByClause = [literal("DATE(createdAt)"), "subscriptionStatus"];
      attributes = [
        [fn("DATE", col("createdAt")), "date"],
        "subscriptionStatus",
        [fn("COUNT", col("id")), "count"],
      ];
    } else {
      // Group by ISO week
      groupByClause = [
        literal("YEAR(createdAt)"),
        literal("WEEK(createdAt, 1)"),
        "subscriptionStatus",
      ];
      attributes = [
        [fn("YEAR", col("createdAt")), "year"],
        [fn("WEEK", col("createdAt"), 1), "week"],
        "subscriptionStatus",
        [fn("COUNT", col("id")), "count"],
      ];
    }

    const rawData = await UserModel.findAll({
      where: {
        createdAt: {
          [Op.gte]: fromDate,
        },
      },
      attributes,
      group: groupByClause,
      order:
        type === "week"
          ? [[literal("DATE(createdAt)"), "ASC"]]
          : [
              [literal("YEAR(createdAt)"), "ASC"],
              [literal("WEEK(createdAt, 1)"), "ASC"],
            ],
    });

    if (type === "week") {
      // Build per-date status map
      const result = {};
      for (let i = 0; i < 7; i++) {
        const date = format(addDays(fromDate, i), "yyyy-MM-dd");
        result[date] = { subscribed: 0, not_subscribed: 0, cancelled: 0 };
      }

      rawData.forEach((row) => {
        const date = format(new Date(row.getDataValue("date")), "yyyy-MM-dd");
        const status = row.getDataValue("subscriptionStatus");
        const count = parseInt(row.getDataValue("count"));
        if (result[date]) {
          result[date][status] = count;
        }
      });

      return Object.entries(result).map(([date, counts]) => ({
        date,
        ...counts,
      }));
    } else {
      // Group by week
      const weekMap = {};
      rawData.forEach((row) => {
        const year = row.getDataValue("year");
        const week = row.getDataValue("week");
        const status = row.getDataValue("subscriptionStatus");
        const count = parseInt(row.getDataValue("count"));
        const key = `${year}-${week}`;

        if (!weekMap[key]) {
          weekMap[key] = { subscribed: 0, not_subscribed: 0, cancelled: 0 };
        }
        weekMap[key][status] = count;
      });

      // Generate 5 full weeks
      const weeks = [];
      const start = startOfWeek(fromDate, { weekStartsOn: 1 });

      for (let i = 0; i < 5; i++) {
        const weekStart = addDays(start, i * 7);
        const year = parseInt(format(weekStart, "RRRR"));
        const week = parseInt(format(weekStart, "II"));
        const key = `${year}-${week}`;

        weeks.push({
          date: `Week ${i + 1}`,
          ...(weekMap[key] || {
            subscribed: 0,
            not_subscribed: 0,
            cancelled: 0,
          }),
        });
      }

      return weeks;
    }
  },
};
