const Sequelize = require("sequelize");
const { PAGE_LIMIT } = require("../../Config/constants");
const { Op } = require("sequelize");
const UserModel = require("../../Database/Models").user;
const breathWorkProgressModel =
  require("../../Database/Models").breathWorkProgress;
const RecommendationModel = require("../../Database/Models").recommendation;
const VideoActivityModel =
  require("../../Database/Models").videoActivityProgress;

module.exports = {
  getUserList: async (reqParams) => {
    const offset = (reqParams.page - 1) * PAGE_LIMIT;
    const limit = PAGE_LIMIT;
    let sortOrder =
      reqParams.sort_order === undefined ? "desc" : reqParams.sort_order;
    let sortBy = reqParams.sort_by === undefined ? "id" : reqParams.sort_by;
    let search = reqParams.search === undefined ? "" : reqParams.search;

    let whereCondition = [{ deletedAt: null }];
    if (search) {
      whereCondition.push({
        [Sequelize.Op.or]: [
          { firstName: { [Sequelize.Op.like]: `%${search}%` } },
          { lastName: { [Sequelize.Op.like]: `%${search}%` } },
        ],
      });
    }

    const userList = await UserModel.findAndCountAll({
      where: whereCondition,
      attributes: [
        "id",
        "firstName",
        "lastName",
        "email",
        "status",
        "musicPreference",
      ],
      order: [[Sequelize.literal(`${sortBy}`), `${sortOrder}`]],
      offset: offset,
      limit: limit,
    });

    if (userList) {
      return userList;
    }

    return false;
  },

  getUserDataById: async (userId) => {
    const UserData = await UserModel.findOne({
      where: {
        id: userId,
        deletedAt: null,
      },
      attributes: [
        "id",
        "firstName",
        "email",
        "lastName",
        "status",
        "audioPreference",
        "aim",
        "musicPreference",
        "upiId",
      ],
      include: {
        model: RecommendationModel,
        attributes: ["name"],
        as: "recommendation",
      },
    });

    if (UserData) {
      return UserData;
    }
    return false;
  },

  updateUserData: async ({ data, condition }) => {
    const update = await UserModel.update(
      { ...data.userDetails },
      { where: condition }
    );
    if (update[0]) {
      return update;
    }
    return false;
  },

  updateUserStatus: async ({ status, condition }) => {
    const update = await UserModel.update({ status }, { where: condition });
    if (update[0]) {
      return update;
    }
    return false;
  },

  checkUserExist: async (email) => {
    const UserData = await UserModel.findOne({
      where: {
        email: email,
        deletedAt: null,
      },
      attributes: ["id", "firstName", "email", "lastName"],
    });
    if (UserData) {
      return UserData;
    }
    return false;
  },

  createUser: async (payload) => {
    const UserData = await UserModel.create(payload);
    if (UserData) {
      return UserData;
    }
    return false;
  },

  getProgressData: async (payload) => {
    const { userId, endDate } = payload;
    let covertedEndDate = new Date(endDate);
    covertedEndDate.setDate(covertedEndDate.getDate() + 1);
    const startDate = new Date(covertedEndDate);
    startDate.setDate(startDate.getDate() - 7);

    // Fetch progress data within the date range
    const progressData = await breathWorkProgressModel.findAll({
      where: {
        userId: userId, // Filter by userId
        createdAt: {
          [Op.gte]: new Date(startDate), // Greater than or equal to startDate
          [Op.lte]: new Date(covertedEndDate), // Less than or equal to endDate
        },
      },
    });

    if (progressData) {
      return progressData;
    }
    return false;
  },

  userDelete: async (userId) => {
    await breathWorkProgressModel.destroy({ where: { userId } });
    await VideoActivityModel.destroy({ where: { userId } });

    let deletedUser = await UserModel.destroy({ where: { id: userId } });

    if (deletedUser) {
      return deletedUser;
    }
    return false;
  },
};
