const { Sequelize } = require("sequelize");
const { PAGE_LIMIT } = require("../../Config/constants");
const { Op } = require("sequelize");
const AudioModel = require("../../Database/Models").audio;

module.exports = {
    addAudio: async (payload) => {
        const audioData = await AudioModel.create(payload);
        if (audioData) {
            return audioData;
        }
        return false
    },

    getList: async (reqParams) => {
        const offset = (reqParams.page - 1) * PAGE_LIMIT;
        const limit = PAGE_LIMIT;
        let sortOrder =
            reqParams.sort_order === undefined ? 'desc' : reqParams.sort_order;
        let sortBy = reqParams.sort_by === undefined ? 'id' : reqParams.sort_by;
        let search = reqParams.search === undefined ? "" : reqParams.search;

        let whereCondition = {
            deletedAt: null,
            title: {
                [Op.ne]: 'No Sound', // Exclude "No Sound"
                ...(search ? { [Op.like]: `%${search}%` } : {})
            }
        };

        const audioList = await AudioModel.findAndCountAll({
            where: whereCondition,
            order: [[Sequelize.literal(`${sortBy}`), `${sortOrder}`]],
            offset: offset,
            limit: limit
        })

        if (audioList) {
            return audioList;
        }

        return false
    },

    getAudioById: async (id) => {
        const audioData = await AudioModel.findOne({
            where: {
                id,
                deletedAt: null
            }
        });
        if (audioData) {
            return audioData;
        }
        return false
    },

    deleteAudio: async (id) => {
        const deletedAudio = await AudioModel.update({
            deletedAt: Date.now()
        }, {
            where: { id }
        });
        if (deletedAudio) {
            return deletedAudio;
        }
        return false
    },

    editAudio: async (id, payload) => {
        const [updatedRows] = await AudioModel.update(payload, {
            where: { id }
        });
        if (updatedRows) {
            return updatedRows;
        }
        return false
    },
}
