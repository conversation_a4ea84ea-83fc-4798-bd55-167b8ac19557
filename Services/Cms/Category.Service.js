const { Sequelize, Op, where } = require("sequelize");
const { PAGE_LIMIT, S3_VIDEO_FOLDER, S3_CATEGORY_FOLDER } = require("../../Config/constants");
const { s3Delete } = require("../../Utils/s2helpers");

const CategoryModel = require("../../Database/Models").category;
const PlaylistModel = require("../../Database/Models").playlist;
const RecommendationModel = require("../../Database/Models").recommendation;
const RecommendationCateListModel = require("../../Database/Models").recommendationCateList;

// Function to find the root category (the topmost parent)
const getRootCategory = async (category) => {
    let rootCategory = category;
    while (rootCategory.parentId) {
        const parentCategory = await CategoryModel.findOne({
            where: { id: rootCategory.parentId, deletedAt: null }, // Ensure parent category is not deleted
            attributes: ['id', 'name', 'parentId'] // Include parentId for further recursion
        });
        if (parentCategory) {
            rootCategory = parentCategory; // Move up to the next parent
        } else {
            break; // Stop if no parent is found
        }
    }
    return rootCategory; // Return the topmost root category
};

module.exports = {
    getCategoryByName: async (name) => {
        const categoryData = await CategoryModel.findOne({
            where: {
                name,
                deletedAt: null,
            },
        });
        if (categoryData) {
            return categoryData;
        }
        return false;
    },

    createCategory: async (payload) => {
        const newCategory = await CategoryModel.create(payload);

        if (newCategory) {
            return newCategory;
        }
        return false;
    },

    getCategoryList: async (reqParams) => {
        const offset = (reqParams.page - 1) * PAGE_LIMIT;
        const limit = PAGE_LIMIT;
        let sortOrder =
            reqParams.sort_order === undefined ? "desc" : reqParams.sort_order;
        let sortBy = reqParams.sort_by === undefined ? "id" : reqParams.sort_by;
        let search = reqParams.search === undefined ? "" : reqParams.search;

        const count = await CategoryModel.count({
            where: {
                parentId: null,
                deletedAt: null,
            },
        });

        const getCategoryHierarchy = async (parentId = null) => {
            const categories = await CategoryModel.findAll({
                where: {
                    parentId,
                    [Op.or]: [
                        { name: { [Op.like]: `%${search}%` } },
                        { subTitle: { [Op.like]: `%${search}%` } },
                    ],
                },
                include: [
                    {
                        model: PlaylistModel,
                        as: "playlists",
                    },
                ],
                order: [[sortBy, sortOrder]],
                limit,
                offset,
            });

            const result = await Promise.all(
                categories.map(async (cat) => {
                    const subcategories = await getCategoryHierarchy(cat.id);

                    const exerciseDurationTime = cat.exerciseDurationTime
                        ? cat.exerciseDurationTime.split(",").map(Number)
                        : [];

                    return {
                        id: cat.id,
                        name: cat.name,
                        subTitle: cat.subTitle,
                        description: cat.description,
                        subcategories,
                        playlists: cat.playlists || [],
                        haveSubCategory: cat.haveSubCategory,
                        bannerImage: cat.bannerImage,
                        holdTime: cat.holdTime,
                        inhaleTime: cat.inhaleTime,
                        exhaleTime: cat.exhaleTime,
                        parentId: cat.parentId,
                        haveExercise: cat.haveExercise,
                        exerciseDurationTime: exerciseDurationTime,
                    };
                })
            );

            return result;
        };

        const hierarchy = await getCategoryHierarchy();

        if (hierarchy) {
            return { count: count, rows: hierarchy };
        }
        return false;
    },

    getCategoryById: async (id) => {
        const categoryData = await CategoryModel.findOne({
            where: {
                id,
                deletedAt: null,
            },
        });

        if (categoryData) {
            // Convert exerciseDurationTime string to an array of numbers if it exists
            const exerciseDurationTime = categoryData.exerciseDurationTime
                ? categoryData.exerciseDurationTime.split(",").map(Number)
                : [];

            return {
                ...categoryData.toJSON(), // Convert the Sequelize model to plain object
                exerciseDurationTime, // Add the converted array to the response
            };
        }

        return false;
    },

    createPlaylist: async (payload) => {
        const newPlaylist = await PlaylistModel.create(payload);

        if (newPlaylist) {
            return newPlaylist;
        }
        return false;
    },

    getPlaylistById: async (id) => {
        const playlistData = await PlaylistModel.findOne({
            where: {
                id,
                deletedAt: null,
            },
        });
        if (playlistData) {
            return playlistData;
        }
        return false;
    },

    editCategory: async (id, payload) => {
        const [updatedRows] = await CategoryModel.update(payload, {
            where: { id },
        });
        if (updatedRows) {
            return updatedRows;
        }
        return false;
    },

    deleteCategory: async (id) => {
        try {
            const idsToDelete = [];

            // Step 1: Collect all category IDs recursively
            const collectCategoryIds = async (categoryId) => {
                const category = await CategoryModel.findByPk(categoryId, { paranoid: false });
                if (!category) return; // If invalid ID, skip it

                idsToDelete.push(categoryId);

                const children = await CategoryModel.findAll({
                    where: { parentId: categoryId },
                    attributes: ['id'],
                    raw: true,
                    paranoid: false,
                });

                for (const child of children) {
                    await collectCategoryIds(child.id);
                }
            };

            await collectCategoryIds(id);

            if (!idsToDelete.length) {
                return false; // Nothing to delete
            }

            // Step 2: Get associated playlists
            const playlists = await PlaylistModel.findAll({
                where: { categoryId: idsToDelete },
                attributes: ['file', 'banner'],
                raw: true,
            });

            // Step 3: Delete playlist files from S3
            for (const playlist of playlists) {
                if (playlist?.file) await s3Delete(`${S3_VIDEO_FOLDER}/${playlist?.file}`);
                if (playlist?.banner) await s3Delete(`${S3_CATEGORY_FOLDER}/${playlist?.banner}`);
            }

            // Step 4: Get categories to delete bannerImages
            const categories = await CategoryModel.findAll({
                where: { id: idsToDelete },
                attributes: ['bannerImage'],
                raw: true,
            });

            for (const category of categories) {
                if (category?.bannerImage) await s3Delete(`${S3_CATEGORY_FOLDER}/${category?.bannerImage}`);
            }

            // Step 5: Soft delete categories
            await CategoryModel.update(
                { deletedAt: new Date() },
                { where: { id: idsToDelete } }
            );

            // Step 6: Soft delete playlists
            await PlaylistModel.update(
                { deletedAt: new Date() },
                { where: { categoryId: idsToDelete } }
            );

            return true;
        } catch (error) {
            console.error('Error while deleting category and associated data:', error);
            return false;
        }
    },

    getCategoryListHaveParent: async (id) => {
        const recommendationData = await RecommendationModel.findOne({
            where: {
                id
            },
            include: {
                model: RecommendationCateListModel,
                as: "categories",
                paranoid: false
            }
        });
        return recommendationData || null
    },

    addRecommendation: async (payload) => {
        const addedData = await RecommendationModel.update(
            {
                name: payload.name
            },
            {
                where: {
                    id: payload.id,
                    deletedAt: null
                }
            }
        );
        const categoryIds = payload.recommendations.map(rec => rec.id);
        await RecommendationCateListModel.update(
            { deletedAt: Date.now() },
            {
                where: {
                    recommendationId: payload.id,
                    id: {
                        [Op.notIn]: categoryIds,
                    },
                    deletedAt: null,
                },
            }
        );
        let data = await RecommendationCateListModel.update(
            { deletedAt: null },
            {
                where: {
                    recommendationId: payload.id,
                    id: {
                        [Op.in]: categoryIds,
                    },
                },
                paranoid: false
            }
        );

        // Check if the update was successful
        if (!addedData || addedData[0] === 0) {
            return false
        }

        return addedData
    },

    getRecommendationDetails: async (id) => {
        const recommendationData = await RecommendationModel.findOne({
            where: {
                id
            },
            include: {
                model: RecommendationCateListModel,
                as: "categories",
                where: {
                    deletedAt: null
                },
                required: false,
                paranoid: false
            }
        });
        return recommendationData || null
    },

    getRecommendationList: async (reqParams) => {
        const offset = (reqParams.page - 1) * PAGE_LIMIT;
        const limit = PAGE_LIMIT;
        let sortOrder =
            reqParams.sort_order === undefined ? "desc" : reqParams.sort_order;
        let sortBy = reqParams.sort_by === undefined ? "id" : reqParams.sort_by;
        let search = reqParams.search === undefined ? "" : reqParams.search;

        const recommendationList = await RecommendationModel.findAndCountAll({
            where: {
                [Op.or]: [
                    { name: { [Op.like]: `%${search}%` } },
                ],
            },
            order: [[sortBy, sortOrder]],
            limit,
            offset,
        });

        if (recommendationList && recommendationList?.count) {
            return recommendationList
        }

        return false
    },

    changeVideoStatus: async (video_name, status) => {
        await PlaylistModel.update({
            videoStatus: status
        }, {
            where: {
                file: video_name
            }
        })
    },

    deletePlaylist: async (id) => {
        let data = await PlaylistModel.destroy({
            where: { id }
        });
        return data
    },

    editPlaylist: async (id, payload) => {
        const [updatedRows] = await PlaylistModel.update(payload, {
            where: { id, deletedAt: null },
        });
        return updatedRows
    }
};
