const { Op } = require("sequelize");
const db = require("../../Database/Models");
const { startOfMonth, endOfMonth, format, parseISO } = require("date-fns");

module.exports = {
  checkMonthlyEntry: async (userId, referralDate) => {
    // Parse the provided date and get the month/year
    const providedDate = parseISO(referralDate);
    const firstDayOfMonth = format(startOfMonth(providedDate), "yyyy-MM-dd");
    const lastDayOfMonth = format(endOfMonth(providedDate), "yyyy-MM-dd");

    // Check if user already has an entry in the same month as the provided date
    const existingEntry = await db.userReferral.findOne({
      where: {
        user_id: userId,
        date: {
          [Op.between]: [firstDayOfMonth, lastDayOfMonth],
        },
        deletedAt: null,
      },
    });

    return !!existingEntry;
  },

  addUserReferral: async (data) => {
    return await db.userReferral.create(data);
  },

  getUserReferralList: async ({
    page,
    sort_by,
    sort_order,
    search,
    month,
    year,
    user_id,
  }) => {
    const limit = 10;
    const offset = (page - 1) * limit;

    let whereCondition = {
      deletedAt: null,
    };

    if (search) {
      whereCondition = {
        ...whereCondition,
        [Op.or]: [
          { "$user.firstName$": { [Op.like]: `%${search}%` } },
          { "$user.lastName$": { [Op.like]: `%${search}%` } },
        ],
      };
    }

    if (user_id) {
      whereCondition = {
        ...whereCondition,
        user_id,
      };
    }

    // Add month and year filtering based on the date field
    if (month && year) {
      const startDate = format(new Date(year, month - 1, 1), "yyyy-MM-dd");
      const endDate = format(
        endOfMonth(new Date(year, month - 1, 1)),
        "yyyy-MM-dd"
      );

      whereCondition = {
        ...whereCondition,
        date: {
          [Op.between]: [startDate, endDate],
        },
      };
    } else if (year) {
      const startDate = format(new Date(year, 0, 1), "yyyy-MM-dd");
      const endDate = format(new Date(year, 11, 31), "yyyy-MM-dd");

      whereCondition = {
        ...whereCondition,
        date: {
          [Op.between]: [startDate, endDate],
        },
      };
    }

    const { count, rows } = await db.userReferral.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: db.user,
          as: "user",
          attributes: ["id", "firstName", "lastName", "email"],
        },
      ],
      order: [[sort_by, sort_order]],
      limit,
      offset,
      distinct: true,
    });

    return {
      count,
      rows,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    };
  },

  getReferralsByMonth: async (month, year) => {
    const startDate = format(new Date(year, month - 1, 1), "yyyy-MM-dd");
    const endDate = format(
      endOfMonth(new Date(year, month - 1, 1)),
      "yyyy-MM-dd"
    );

    const referrals = await db.userReferral.findAll({
      where: {
        date: {
          [Op.between]: [startDate, endDate],
        },
        deletedAt: null,
      },
      include: [
        {
          model: db.user,
          as: "user",
          attributes: ["id", "firstName", "lastName", "email"],
        },
      ],
      order: [["date", "DESC"]],
    });

    return referrals;
  },

  getMonthlyStats: async (year) => {
    const stats = await db.userReferral.findAll({
      attributes: [
        [db.sequelize.fn("MONTH", db.sequelize.col("date")), "month"],
        [db.sequelize.fn("COUNT", db.sequelize.col("id")), "count"],
        [db.sequelize.fn("SUM", db.sequelize.col("amount")), "totalAmount"],
      ],
      where: {
        [Op.and]: [
          db.sequelize.where(
            db.sequelize.fn("YEAR", db.sequelize.col("date")),
            year
          ),
          { deletedAt: null },
        ],
      },
      group: [db.sequelize.fn("MONTH", db.sequelize.col("date"))],
      order: [[db.sequelize.fn("MONTH", db.sequelize.col("date")), "ASC"]],
    });

    return stats;
  },
};
