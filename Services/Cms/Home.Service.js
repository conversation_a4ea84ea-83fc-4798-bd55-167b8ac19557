const { Sequelize } = require("sequelize");
const { PAGE_LIMIT } = require("../../Config/constants");

const AppContentModel = require("../../Database/Models").appContent;


module.exports = {
    uploadImage: async (payload) => {
        const imageData = await AppContentModel.create(payload);
        if (imageData) {
            return imageData;
        }
        return false
    },

    getBannerList: async (reqParams) => {
        const offset = (reqParams.page - 1) * PAGE_LIMIT;
        const limit = PAGE_LIMIT;
        let sortOrder =
            reqParams.sort_order === undefined ? 'desc' : reqParams.sort_order;
        let sortBy = reqParams.sort_by === undefined ? 'id' : reqParams.sort_by;

        let whereCondition = [{ deletedAt: null }];

        const userList = await AppContentModel.findAndCountAll({
            where: whereCondition,
            order: [[Sequelize.literal(`${sortBy}`), `${sortOrder}`]],
            offset: offset,
            limit: limit
        })

        if (userList) {
            return userList;
        }

        return false
    }
}
