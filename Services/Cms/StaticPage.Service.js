const StaticPageModel = require("../../Database/Models").staticPage;
const { Op, Sequelize } = require("sequelize");
const { PAGE_LIMIT } = require("../../Config/constants");

module.exports = {
  getPageList: async (reqParams) => {
    const offset = (reqParams.page - 1) * PAGE_LIMIT;
    const limit = PAGE_LIMIT;
    let sortOrder =
      reqParams.sort_order === undefined ? "desc" : reqParams.sort_order;
    let sortBy = reqParams.sort_by === undefined ? "id" : reqParams.sort_by;
    let search = reqParams.search === undefined ? "" : reqParams.search;
    let whereCondition = [];
    if (search) {
      whereCondition.push({ title: { [Op.like]: `%${search}%` } });
    }
    const pageList = await StaticPageModel.findAndCountAll({
      where: whereCondition,
      order: [[Sequelize.literal(`${sortBy}`), `${sortOrder}`]],
      offset: offset,
      limit: limit,
    });

    if (pageList) {
      return pageList;
    }
    return false;
  },
  getStaticPageData: async (type) => {
    const staticPageData = await StaticPageModel.findOne({
      where: { type: type },
    });
    if (staticPageData) {
      return staticPageData;
    }
    return false;
  },
  getStaticPageDataById: async (pageID) => {
    const staticPageData = await StaticPageModel.findOne({
      where: { id: pageID },
    });
    if (staticPageData) {
      return staticPageData;
    }
    return false;
  },
  addStaticPage: async (data) => {
    const staticPageData = await StaticPageModel.create({ ...data });
    if (staticPageData) {
      return staticPageData;
    }
    return false;
  },
  updatePageData: async ({ data, condition }) => {
    const update = await StaticPageModel.update(
      { ...data },
      { where: condition }
    );
    if (update[0]) {
      return update;
    }
    return false;
  },
  deleteStaticPageDataById: async (pageID) => {
    const deletedStaticPageData = await StaticPageModel.destroy({
      where: { id: pageID },
    });
    if (deletedStaticPageData) {
      return deletedStaticPageData;
    }
    return false;
  },
};
