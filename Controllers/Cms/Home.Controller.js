const { asyncHandler } = require("../../Utils/asyncHandler.js");
const HomeService = require("../../Services/Cms/Home.Service.js");
const { FILE_SIZE, S3_IMAGE_FOLDER, S3_PROJECT_FOLDER } = require("../../Config/constants.js");
const { s3Upload } = require("../../Utils/s2helpers.js");

module.exports = {
    add: asyncHandler(async (req, res) => {
        if (req?.file) {
            const bannerFile = req.file;
            if (bannerFile.size > FILE_SIZE) {
                return res.handler.badRequest('VALIDATION.BANNER.FILE_SIZE');
            }
            const { mimetype, originalname } = bannerFile;
            const folder = S3_PROJECT_FOLDER + "/" + S3_IMAGE_FOLDER
            await s3Upload(folder, originalname, mimetype);
            bannerImage = originalname;
        }

        if (bannerImage) req.body.bannerImage = bannerImage;
        req.body.isActive = true
        req.body.title = "Image"
        const uploadedImage = await HomeService.uploadImage(req.body)

        if (uploadedImage) {
            return res.handler.success(
                "CMS.IMAGE.UPLOADED_SUCCESSFULLY",
                uploadedImage
            );
        }
        return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }),

    list: asyncHandler(async (req, res) => {
        const bannerList = await HomeService.getBannerList(req?.body);

        if (!bannerList) {
            return res.handler.notFound('CMS.BANNER.NOT_FOUND')
        }

        return res.handler.success('CMS.BANNER.LIST_FETCHED', bannerList);
    }),
};
