const { asyncHandler } = require("../../Utils/asyncHandler.js");
const ContactUsService = require("../../Services/Cms/ContactUs.Service.js");
const sendEmail = require("../../Utils/sendEmail");

module.exports = {
    list: asyncHandler(async (req, res) => {
        const contactUsList = await ContactUsService.getContactUsList(req?.body);

        if (!contactUsList) {
            return res.handler.notFound("CMS.CONTACT_US.NOT_FOUND");
        }

        return res.handler.success("CMS.CONTACT_US.LIST_FETCHED", contactUsList);
    }),
    contactUsDetails: asyncHandler(async (req, res) => {
        const contactUsData = await ContactUsService.getContactUsDataById(
            req.body.id
        );

        if (contactUsData) {
            return res.handler.success(
                "CMS.CONTACT_US.DETAILS_FETCHED",
                contactUsData
            );
        }
        return res.handler.notFound("CMS.USER.NOT_FOUND");
    }),
    reply: asyncHandler(async (req, res) => {
        await sendEmail(res, req.body.email, req.body.subject, req.body.body);

        return res.handler.success("USER.CONTACT_US.MAIL_SENT_SUCCESSFULLY", {});
    }),

    delete: asyncHandler(async (req, res) => {
        let deletedContactUs = await ContactUsService.contactUsDelete(req.query.id)
        if (deletedContactUs) {
            return res.handler.success('USER.CONTACT_US.DELETED_SUCCESSFULLY', deletedContactUs);
        }
        return res.handler.notFound('USER.CONTACT_US.NOT_FOUND');
    })

};
