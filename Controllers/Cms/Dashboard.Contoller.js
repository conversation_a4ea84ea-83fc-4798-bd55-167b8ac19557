const { asyncHandler } = require("../../Utils/asyncHandler");
const DashboardService = require("../../Services/Cms/Dashboard.Service");

module.exports = {
  getStats: asyncHandler(async (req, res) => {
    const [totalUsers, breathworkViews, athleticViews, subscribedUsers] =
      await Promise.all([
        DashboardService.getTotalUser(),
        DashboardService.getBreathworkViews(),
        DashboardService.getAthleticBreathworkViews(),
        DashboardService.getTotalSubscribedUser(),
      ]);

    return res.handler.success("CMS.STATS.LIST_FETCHED", {
      totalUsers,
      breathworkViews,
      athleticViews,
      subscribedUsers,
    });
  }),

  getUserStat: async (req, res) => {
    const userStat = await DashboardService.getUsersStat(req.query.type);

    if (userStat) {
      return res.handler.success("CMS.USER_STAT.FETCHED", userStat);
    }

    return res.handler.badRequest("CMS.USER_STAT.NOT_FOUND");
  },

  getBreatworkProgressStat: asyncHandler(async (req, res) => {
    const breatworkProgressStat =
      await DashboardService.getbreatworkProgressStat(req.query.type);

    if (breatworkProgressStat) {
      return res.handler.success(
        "CMS.BREATHWORK_PROGRESS_STAT.FETCHED",
        breatworkProgressStat
      );
    }

    return res.handler.badRequest("CMS.BREATHWORK_PROGRESS_STAT.NOT_FOUND");
  }),

  getSubscriptionStatusStats: asyncHandler(async (req, res) => {
    const { type } = req.query; // week or month
    const data = await DashboardService.getSubscriptionStats(type);
    if (data) {
      return res.handler.success("CMS.SUBSCRIPTION_STAT.FETCHED", data);
    }

    return res.handler.badRequest("CMS.SUBSCRIPTION_STAT.NOT_FOUND");
  }),
};
