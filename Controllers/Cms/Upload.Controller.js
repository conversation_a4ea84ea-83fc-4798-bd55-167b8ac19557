const AWS = require("aws-sdk")
const { S3_BUCKET_NAME, S3_PROJECT_FOLDER, S3_ACCESS_KEY, S3_SECRET_KEY, S3_URL, S3_VIDEO_FOLDER } = require("../../Config/constants")
const { orderBy } = require("lodash")
const CategoryService = require("../../Services/Cms/Category.Service");

const s3Credentials = new AWS.Credentials({
    accessKeyId: S3_ACCESS_KEY,
    secretAccessKey: S3_SECRET_KEY,
})

const s3 = new AWS.S3({
    region: 'ap-south-1', // or your actual region
    credentials: s3Credentials,
    signatureVersion: 'v4',
})

const BUCKET_NAME = S3_BUCKET_NAME
const FOLDER_NAME = S3_PROJECT_FOLDER

const UploadController = {
    initializeMultipartUpload: async (req, res) => {
        const { name } = req.body
        const multipartParams = {
            Bucket: BUCKET_NAME,
            Key: `${FOLDER_NAME}/${S3_VIDEO_FOLDER}/${name}`,
            ACL: "public-read",
        }

        const multipartUpload = await s3.createMultipartUpload(multipartParams).promise()
        await CategoryService.changeVideoStatus(name, "Uploading")
        res.send({
            fileId: multipartUpload.UploadId,
            fileKey: multipartUpload.Key,
        })
    },

    getMultipartPreSignedUrls: async (req, res) => {
        const { fileKey, fileId, parts } = req.body
        const multipartParams = {
            Bucket: BUCKET_NAME,
            Key: fileKey,
            UploadId: fileId,
        }

        const promises = []

        for (let index = 0; index < parts; index++) {
            promises.push(
                s3.getSignedUrlPromise("uploadPart", {
                    ...multipartParams,
                    PartNumber: index + 1,
                }),
            )
        }

        const signedUrls = await Promise.all(promises)

        const partSignedUrlList = signedUrls.map((signedUrl, index) => {
            return {
                signedUrl: signedUrl,
                PartNumber: index + 1,
            }
        })

        res.send({
            parts: partSignedUrlList,
        })
    },

    finalizeMultipartUpload: async (req, res) => {
        const { fileId, fileKey, parts } = req.body
        const multipartParams = {
            Bucket: BUCKET_NAME,
            Key: fileKey,
            UploadId: fileId,
            MultipartUpload: {
                // ordering the parts to make sure they are in the right order
                Parts: orderBy(parts, ["PartNumber"], ["asc"]),
            },
        }
        await s3.completeMultipartUpload(multipartParams).promise().catch((err) => {
            console.log(err)
        })
        let nameArray = fileKey.split('/');
        let name = nameArray[nameArray.length - 1];
        await CategoryService.changeVideoStatus(name, "uploaded")
        res.send()
    },
}

module.exports = { UploadController }
