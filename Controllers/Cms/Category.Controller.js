const { asyncHandler } = require("../../Utils/asyncHandler");
const CategoryService = require("../../Services/Cms/Category.Service");
const { S3_CATEGORY_FOLDER, FILE_SIZE, VIDEO_FILE_SIZE, S3_VIDEO_FOLDER, S3_PROJECT_FOLDER } = require("../../Config/constants");
const { s3Upload, s3Delete } = require("../../Utils/s2helpers");

module.exports = {
    add: asyncHandler(async (req, res) => {

        const checkCategoryExist = await CategoryService.getCategoryByName(req.body.name);
        req.body.holdTime = req.body.holdTime ? req.body.holdTime : 0;
        req.body.inhaleTime = req.body.inhaleTime ? req.body.inhaleTime : 0;
        req.body.exhaleTime = req.body.exhaleTime ? req.body.exhaleTime : 0;
        req.body.exerciseDurationTime = req.body.exerciseDurationTime ? req.body.exerciseDurationTime : null

        if (checkCategoryExist) {
            return res.handler.badRequest("CMS.CATEGORY.ALREADY_EXIST")
        }
        let bannerImage;

        if (req?.file) {
            const bannerFile = req.file;
            if (bannerFile.size > FILE_SIZE) {
                return res.handler.badRequest('VALIDATION.BANERIMAGE.FILE_SIZE');
            }
            const { mimetype, originalname } = bannerFile;
            const folder = S3_PROJECT_FOLDER + "/" + S3_CATEGORY_FOLDER
            await s3Upload(folder, originalname, mimetype);
            bannerImage = originalname;
        }
        if (bannerImage) req.body.bannerImage = bannerImage;

        const createdCategory = await CategoryService.createCategory(req.body)

        if (createdCategory) {
            return res.handler.success(
                "CMS.CATEGORY.ADDED_SUCCESSFULLY",
                createdCategory
            );
        }
        return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }),

    list: asyncHandler(async (req, res) => {
        const categoryList = await CategoryService.getCategoryList(
            ({ search, page, sort_by, sort_order } = req.body)
        );

        if (categoryList) {
            return res.handler.success("CMS.CATEGORY.LIST_FETCHED", categoryList);
        }
        return res.handler.success("CMS.CATEGORY.NO_DATA_FOUND");
    }),

    categoryDetails: asyncHandler(async (req, res) => {
        const categoryData = await CategoryService.getCategoryById(req.body.id);

        if (!categoryData) {
            return res.handler.badRequest("CMS.CATEGORY.NOT_FOUND")
        }

        return res.handler.success("CMS.CATEGORY.DETAILS_FETCHED", categoryData);
    }),

    addPlaylist: asyncHandler(async (req, res) => {
        const categoryData = await CategoryService.getCategoryById(req.body.categoryId);

        if (!categoryData) {
            return res.handler.badRequest("CMS.CATEGORY.NOT_FOUND")
        }

        let banner;
        let video;

        if (req.files['banner'] && req.files['banner'][0]) {
            const bannerImage = req.files['banner'][0];

            if (bannerImage.size > FILE_SIZE) {
                return res.handler.badRequest('VALIDATION.ORGANIZER.FILE_SIZE');
            }

            const { mimetype, originalname } = bannerImage;
            const folder = S3_PROJECT_FOLDER + "/" + S3_CATEGORY_FOLDER
            await s3Upload(folder, originalname, bannerImage.buffer, mimetype);
            banner = originalname;
        }

        if (req.files['file'] && req.files['file'][0]) {
            const videoFile = req.files['file'][0];
            const { mimetype, originalname } = videoFile;
            video = originalname;
        }

        if (banner) req.body.banner = banner;
        if (video) req.body.file = video;

        // Check if ID is provided for update operation
        if (req.body.id) {
            const existingPlaylist = await CategoryService.getPlaylistById(req.body.id);

            if (!existingPlaylist) {
                return res.handler.badRequest("CMS.PLAYLIST.NOT_FOUND")
            }

            // Delete old files if new ones are uploaded
            if (video && existingPlaylist.file) {
                const folder = S3_PROJECT_FOLDER + "/" + S3_VIDEO_FOLDER
                await s3Delete(folder, existingPlaylist.file);
            }

            if (banner && existingPlaylist.banner) {
                const folder = S3_PROJECT_FOLDER + "/" + S3_CATEGORY_FOLDER
                await s3Delete(folder, existingPlaylist.banner);
            }

            const updatedPlaylist = await CategoryService.editPlaylist(req.body.id, req.body)

            if (updatedPlaylist) {
                return res.handler.success("CMS.PLAYLIST.UPDATED_SUCCESSFULLY", updatedPlaylist);
            }
        } else {
            // Create new playlist
            const playlistData = await CategoryService.createPlaylist(req.body)

            if (playlistData) {
                return res.handler.success("CMS.PLAYLIST.ADDED_SUCCESSFULLY", playlistData);
            }
        }

        return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }),

    getPlaylist: asyncHandler(async (req, res) => {
        const playlistData = await CategoryService.getPlaylistById(req.body.id);

        if (!playlistData) {
            return res.handler.badRequest("CMS.CATEGORY.NOT_FOUND")
        }

        return res.handler.success("CMS.PLAYLIST.ADDED_SUCCESSFULLY", playlistData);
    }),

    edit: asyncHandler(async (req, res) => {
        const categoryData = await CategoryService.getCategoryById(req.body.id);

        if (!categoryData) {
            return res.handler.badRequest("CMS.CATEGORY.NOT_FOUND")
        }
        let bannerImage;

        if (req?.file) {
            const organizerFile = req.file;
            if (organizerFile.size > FILE_SIZE) {
                return res.handler.badRequest('VALIDATION.ORGANIZER.FILE_SIZE');
            }
            const { mimetype, originalname } = organizerFile;
            const folder = S3_PROJECT_FOLDER + "/" + S3_CATEGORY_FOLDER
            await s3Upload(folder, originalname, mimetype);
            bannerImage = originalname;
        }

        if (bannerImage) req.body.bannerImage = bannerImage;
        const categoryEditedData = await CategoryService.editCategory(req.body.id, req.body);

        if (categoryEditedData) {
            return res.handler.success("CMS.CATEGORY.UPDATED_SUCCESSFULLY", categoryEditedData)
        }

        return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }),

    delete: asyncHandler(async (req, res) => {
        const categoryData = await CategoryService.getCategoryById(req.body.id);

        if (categoryData) {
            const deletedCategory = await CategoryService.deleteCategory(req.body.id)

            if (deletedCategory) {
                return res.handler.success("CMS.CATEGORY.DELETED_SUCCESSFULLY", deletedCategory)
            }

            return res.handler.badRequest("GENERAL.SOMETHING_WRONG");

        }
        return res.handler.badRequest("CMS.CATEGORY.NOT_FOUND")
    }),

    listHaveParent: asyncHandler(async (req, res) => {
        const categoryData = await CategoryService.getCategoryListHaveParent(req.body.id);

        if (categoryData) {
            return res.handler.success("CMS.CATEGORY.LIST_FETCHED", categoryData)
        }

        return res.handler.badRequest("CMS.CATEGORY.NOT_FOUND")
    }),

    addRecommendation: asyncHandler(async (req, res) => {
        const addRecommendationData = await CategoryService.addRecommendation(req.body)

        if (addRecommendationData) {
            return res.handler.success("CMS.CATEGORY.RECOMMENDATION_ADDED", addRecommendationData)
        }

        return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }),

    getRecommendationDetails: asyncHandler(async (req, res) => {
        const recommendationDetails = await CategoryService.getRecommendationDetails(req.body.id);

        if (recommendationDetails) {
            return res.handler.success("CMS.CATEGORY.RECOMMENDATION_DETAILS_FETCHED", recommendationDetails)
        }
        return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }),

    listRecommendation: asyncHandler(async (req, res) => {
        const recommendationList = await CategoryService.getRecommendationList(
            ({ search, page, sort_by, sort_order } = req.body)
        );

        if (recommendationList) {
            return res.handler.success("CMS.CATEGORY_RECOMMENDATION.LIST_FETCHED", recommendationList);
        }
        return res.handler.success("CMS.CATEGORY_RECOMMENDATION.NO_DATA_FOUND");
    }),

    deletePlaylist: asyncHandler(async (req, res) => {
        const playlistData = await CategoryService.getPlaylistById(req.body.id);

        if (playlistData) {
            const deletedPlaylist = await CategoryService.deletePlaylist(req.body.id)

            if (deletedPlaylist) {
                if (playlistData.file) {
                    const folder = S3_PROJECT_FOLDER + "/" + S3_VIDEO_FOLDER
                    await s3Delete(folder, playlistData.file);
                }
                if (playlistData.banner) {
                    const folder = S3_PROJECT_FOLDER + "/" + S3_CATEGORY_FOLDER
                    await s3Delete(folder, playlistData.banner);
                }
                return res.handler.success("CMS.PLAYLIST.DELETED_SUCCESSFULLY", deletedPlaylist)
            }
            return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
        }
        return res.handler.badRequest("CMS.PLAYLIST.NOT_FOUND")
    }),
}