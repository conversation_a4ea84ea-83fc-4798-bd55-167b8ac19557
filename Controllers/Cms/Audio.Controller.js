const { asyncHand<PERSON> } = require("../../Utils/asyncHandler");
const AudioService = require("../../Services/Cms/Audio.Service");
const { VIDEO_FILE_SIZE, S3_AUDIO_FOLDER, S3_PROJECT_FOLDER } = require("../../Config/constants");
const { s3Upload, s3Delete } = require("../../Utils/s2helpers");

module.exports = {
    add: asyncHandler(async (req, res) => {
        let file;

        if (req?.file) {
            const audioFile = req.file;
            // if (audioFile.size > VIDEO_FILE_SIZE) {
            //     return res.handler.badRequest('VALIDATION.AUDIOFILE.FILE_SIZE');
            // }
            const { mimetype, originalname } = audioFile;
            const folder = S3_PROJECT_FOLDER + "/" + S3_AUDIO_FOLDER
            await s3Upload(folder, originalname, mimetype);
            file = originalname;
        }

        if (file) req.body.file = file;

        const createdAudio = await AudioService.addAudio(req.body)

        if (createdAudio) {
            return res.handler.success(
                "CMS.AUDIO.ADDED_SUCCESSFULLY",
                createdAudio
            );
        }
        return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }),

    list: asyncHandler(async (req, res) => {
        const audioList = await AudioService.getList(req?.body);

        if (!audioList) {
            return res.handler.notFound('CMS.AUDIO.NOT_FOUND')
        }

        return res.handler.success('CMS.AUDIO.LIST_FETCHED', audioList);
    }),

    delete: asyncHandler(async (req, res) => {
        const audioData = await AudioService.getAudioById(req.body.id);

        if (audioData) {
            const deletedAudio = await AudioService.deleteAudio(req.body.id)

            if (deletedAudio) {
                return res.handler.success("CMS.AUDIO.DELETED_SUCCESSFULLY", deletedAudio)
            }

            return res.handler.badRequest("GENERAL.SOMETHING_WRONG");

        }
        return res.handler.badRequest("CMS.AUDIO.NOT_FOUND")
    }),
    audioDetails: asyncHandler(async (req, res) => {
        const audioData = await AudioService.getAudioById(req.body.id);
        if (audioData) {
            return res.handler.success("CMS.AUDIO.DETAILS_FETCHED", audioData);
        }
        return res.handler.notFound("CMS.AUDIO.NOT_FOUND");
    }),
    edit: asyncHandler(async (req, res) => {
        const audioData = await AudioService.getAudioById(req.body.id);

        if (!audioData) {
            return res.handler.badRequest("CMS.AUDIO.NOT_FOUND")
        }
        let file;

        if (req?.file) {
            const audioFile = req.file;
            if (audioFile.size > VIDEO_FILE_SIZE) {
                return res.handler.badRequest('VALIDATION.AUDIOFILE.FILE_SIZE');
            }
            const { mimetype, originalname } = audioFile;
            const folder = S3_PROJECT_FOLDER + "/" + S3_AUDIO_FOLDER
            await s3Upload(folder, originalname, mimetype);
            file = originalname;
        }
        if (typeof req?.file == "undefined") {
            const parts = req.body.file.split('/');
            fileValue = parts[parts.length - 1];
            file = fileValue // Get just the filename
        }
        if (file) req.body.file = file;
        const audioEditedData = await AudioService.editAudio(req.body.id, req.body);

        if (audioEditedData) {
            return res.handler.success("CMS.AUDIO.UPDATED_SUCCESSFULLY", audioEditedData)
        }

        return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }),
}