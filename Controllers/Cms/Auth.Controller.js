const { CMS_URL, J<PERSON><PERSON><PERSON><PERSON> } = require("../../Config/constants");
const AuthService = require("../../Services/Cms/Auth.Service");
const { asyncHandler } = require("../../Utils/asyncHandler");
const { generateExpirationToken, generateRandomString } = require("../../Utils/helpers");
const sendEmail = require("../../Utils/sendEmail");
const jwt = require('jsonwebtoken');
const { jwtDecode } = require("jwt-decode");

module.exports = {

	login: asyncHandler(async (req, res) => {
		const loginData = await AuthService.login(req);

		if (loginData?.error) {
			switch (loginData?.error) {
				case 'passwordNotMatch':
					return res.handler.unauthorized('CMS.AUTH.PASSWORD_NOT_MATCH');
				case 'userNotFound':
					return res.handler.badRequest('CMS.AUTH.USER_NOT_FOUND');
				case 'inActive':
					return res.handler.badRequest('CMS.AUTH.INACTIVE');
			}
		}
		return res.handler.success('CMS.AUTH.LOGGED_IN', loginData);
	}),

	getProfile: asyncHandler(async (req, res) => {
		const data = await AuthService.getAdminDataByCondition({
			id: req?.adminId,
			email: req?.email
		});
		if (data) {
			return res.handler.success('CMS.AUTH.DATA_SUCCESS', data);
		}
		return res.handler.badRequest('CMS.AUTH.USER_NOT_FOUND');
	}),

	logout: asyncHandler(async (req, res) => {
		const data = await AuthService.logout(req?.adminId);
		if (data) {
			return res.handler.success('CMS.AUTH.LOGGED_OUT');
		}
		return res.handler.badRequest('GENERAL.SOMETHING_WRONG');
	}),

	updateProfile: asyncHandler(async (req, res) => {
		const { id, ...payload } = req.body;

		const adminData = await AuthService?.getAdminDataByCondition({
			id: Number(id)
		});

		if (!adminData) {
			return res.handler.badRequest('CMS.AUTH.USER_NOT_FOUND');
		}

		const updateData = await AuthService?.update({
			data: payload,
			condition: { id: Number(id) }
		});
		if (updateData) {
			return res.handler.success('CMS.AUTH.PROFILE_UPDATE_SUCCESS', updateData);
		}
		return res.handler.badRequest('GENERAL.SOMETHING_WRONG');
	}),

	changePassword: asyncHandler(async (req, res) => {
		const { newPassword, oldPassword } = req.body;
		const adminData = await AuthService.getAdminById(req?.adminId);

		if (!adminData) {
			return res.handler.badRequest('CMS.AUTH.USER_NOT_FOUND');
		}

		if (newPassword === oldPassword) {
			return res.handler.badRequest('CMS.AUTH.PASSWORD_SAME');
		}

		if (!adminData.validatePassword(oldPassword)) {
			return res.handler.badRequest('CMS.AUTH.PASSWORD_NOT_MATCH');
		}
		const updateData = await AuthService.update({
			data: {
				password: newPassword
			},
			condition: { id: req?.adminId }
		});
		if (updateData) {
			return res.handler.success('CMS.AUTH.PASSWORD_RESET_SUCCESS');
		}
		return res.handler.badRequest('GENERAL.SOMETHING_WRONG');
	}),

	forgotPassword: asyncHandler(async (req, res) => {
		const admin = await AuthService.checkEmailExists(req.body.email)

		if (admin) {
			let token = generateExpirationToken(req.body.email);
			const updateData = await AuthService.updateToken(admin.id, token);

			if (updateData) {
				var subject = "Reset Your BreathWork.ai Admin Password";
				var mailbody =
					"<div><p>We have received a request to reset your password for <b>" +
					req.body.email +
					"</b>.</p>" +
					"<p> If you didn’t make the request, just ignore this email.Otherwise, please reset your password by " +
					'<a href="' +
					CMS_URL +
					"/reset-password/" +
					admin.id +
					"_" +
					generateRandomString(8) +
					"~" +
					token +
					'" >clicking here</a>' +
					".</p>" +
					"<p>For your security, this link expires in 2 hour.</p><p>#Breathwork.ai</p></div>";

				await sendEmail(res, req.body.email.toLowerCase(), subject, mailbody);

				return res.handler.success("CMS.AUTH.ADMIN_FORGOT_PASSWORD_MAIL_SENT", {});
			}
			return res.handler.success('GENERAL.SOMETHING_WRONG')
		}
		return res.handler.badRequest('CMS.AUTH.USER_NOT_FOUND');
	}),

	verifyToken: asyncHandler(async (req, res) => {
		const jwtVerify = await jwt.verify(
			req.body.token,
			JWTTOKEN.secret
		)
		if (jwtVerify) {
			const findToken = await AuthService.getToken(req.body.token)

			if (findToken) {
				return res.handler.success("USER.AUTH.TOKEN_FOUND", {});
			}
		}
		return res.handler.badRequest('CMS.AUTH.USER_TOKEN_NOT_FOUND');
	}),

	resetPassword: asyncHandler(async (req, res) => {
		let userToken = req.params.userToken;

		const JwtDetails = jwtDecode(userToken);

		let admin = await AuthService.findAdmin({
			email: JwtDetails.email,
			otpToken: userToken,
		});

		if (!admin) {
			return res.handler.badRequest('CMS.AUTH.USER_NOT_FOUND');
		}

		let data = await AuthService.update({
			data: { password: req.body.password, otp: null },
			condition: { id: admin.id }
		});

		if (data) {
			return res.handler.success('CMS.AUTH.PROFILE_UPDATE_SUCCESS', data);
		}
		return res.handler.badRequest('GENERAL.SOMETHING_WRONG');
	})
};
