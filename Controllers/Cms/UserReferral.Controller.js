const { asyncHandler } = require("../../Utils/asyncHandler");
const UserReferralService = require("../../Services/Cms/UserReferral.Service");
const {
  S3_PROJECT_FOLDER,
  S3_REFERRAL_FOLDER,
} = require("../../Config/constants");
const { s3Upload, s3Delete } = require("../../Utils/s2helpers");

module.exports = {
  add: asyncHandler(async (req, res) => {
    // Check if user already has a referral entry in the same month as the provided date
    const hasExistingEntry = await UserReferralService.checkMonthlyEntry(
      req.body.user_id,
      req.body.date
    );

    if (hasExistingEntry) {
      return res.handler.badRequest(
        "CMS.USER_REFERRAL.ALREADY_ADDED_FOR_THIS_MONTH"
      );
    }

    // Process image if uploaded
    let imagePath = null;

    if (req?.file) {
      const imageFile = req.file;
      const { mimetype, originalname } = imageFile;
      console.log("originalname: ", originalname);
      const folder = S3_PROJECT_FOLDER + "/" + S3_REFERRAL_FOLDER;
      await s3Upload(folder, originalname, mimetype);
      imagePath = originalname;
    }

    // Create the referral entry
    const referralData = {
      ...req.body,
      image: imagePath,
    };

    const newReferal = await UserReferralService.addUserReferral(referralData);

    if (newReferal) {
      return res.handler.success(
        "CMS.USER_REFERRAL.ADDED_SUCCESSFULLY",
        newReferal
      );
    }

    return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
  }),

  list: asyncHandler(async (req, res) => {
    const referralList = await UserReferralService.getUserReferralList(
      req.body
    );

    if (!referralList) {
      return res.handler.notFound("CMS.USER_REFERRAL.NOT_FOUND");
    }

    return res.handler.success("CMS.USER_REFERRAL.LIST_FETCHED", referralList);
  }),
};
