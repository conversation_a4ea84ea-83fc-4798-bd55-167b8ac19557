const { asyncHandler } = require("../../Utils/asyncHandler.js");
const StaticPageService = require("../../Services/Cms/StaticPage.Service.js");

module.exports = {
  add: asyncHandler(async (req, res) => {
    const checkDataExist = await StaticPageService.getStaticPageData(
      req?.body.type
    );

    if (checkDataExist) {
      return res.handler.badRequest("CMS.STATIC_PAGE.ALREADY_EXIST");
    }

    const newStaticPage = await StaticPageService.addStaticPage(req?.body);

    if (newStaticPage) {
      return res.handler.success(
        "CMS.STATIC_PAGE.ADDED_SUCCESSFULLY",
        newStaticPage
      );
    }
    return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
  }),
  list: asyncHandler(async (req, res) => {
    const pageList = await StaticPageService.getPageList(
      ({ search, page, sort_by, sort_order } = req.body)
    );
    if (pageList.count > 0) {
      return res.handler.success("CMS.PAGE.LIST_FETCHED", pageList);
    }
    return res.handler.success("CMS.NO_DATA_FOUND");
  }),
  pageDetails: asyncHandler(async (req, res) => {
    const pageID = req.body.id;
    const pageData = await StaticPageService.getStaticPageDataById(pageID);
    if (pageData) {
      return res.handler.success("CMS.PAGE.DETAILS_FETCHED", pageData);
    }
    return res.handler.notFound("CMS.PAGE.NOT_FOUND");
  }),
  editPage: asyncHandler(async (req, res) => {
    const { type, title, content } = req.body;
    const pageData = await StaticPageService.getStaticPageDataById(
      req?.body.id
    );
    if (pageData) {
      const updatePageData = await StaticPageService.updatePageData({
        data: { title, type, content },
        condition: { id: req?.body?.id },
      });
      if (updatePageData) {
        return res.handler.success("CMS.PAGE.DETAILS_UPDATED");
      }
      return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    } else {
      return res.handler.notFound("CMS.PAGE.NOT_FOUND");
    }
  }),
  delete: asyncHandler(async (req, res) => {
    const pageID = req.body.id;
    const pageData = await StaticPageService.getStaticPageDataById(pageID);
    if (!pageData) {
      return res.handler.notFound("CMS.PAGE.NOT_FOUND");
    }

    const deletedStaticPage = await StaticPageService.deleteStaticPageDataById(
      pageID
    );
    return res.handler.success(
      "CMS.PAGE.DELETED_SUCCESSFULLY",
      deletedStaticPage
    );
  }),
};
