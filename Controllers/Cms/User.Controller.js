const { asyncHandler } = require("../../Utils/asyncHandler.js");
const UserService = require("../../Services/Cms/USer.Service.js");
const { getBreathWorkProgressDataDayWise } = require("../../Utils/helpers.js");

module.exports = {
	list: asyncHandler(async (req, res) => {
		const userList = await UserService.getUserList(req?.body);

		if (!userList) {
			return res.handler.notFound('CMS.USER.NOT_FOUND')
		}

		return res.handler.success('CMS.USER.LIST_FETCHED', userList);
	}),

	userDetails: asyncHandler(async (req, res) => {
		const userId = req.body.id;
		const userData = await UserService.getUserDataById(userId);

		if (userData) {
			return res.handler.success('CMS.USER.DETAILS_FETCHED', userData);
		}
		return res.handler.notFound('CMS.USER.NOT_FOUND');
	}),

	editUserDetails: asyncHandler(async (req, res) => {
		const { user_id, ...userDetails } = req.body;
		const userData = await UserService.getUserDataById(req?.body.user_id);
		if (userData) {
			const updatedUserData = await UserService.updateUserData({
				data: { userDetails },
				condition: { id: req?.body?.user_id }
			});
			if (updatedUserData) {
				return res.handler.success('CMS.USER.DETAILS_UPDATED');
			}
			return res.handler.badRequest('GENERAL.SOMETHING_WRONG');
		} else {
			return res.handler.notFound('CMS.USER.NOT_FOUND');
		}
	}),

	updateUserStatus: asyncHandler(async (req, res) => {
		const userId = req.body.id;
		const userData = await UserService.getUserDataById(userId);

		if (!userData) {
			return res.handler.notFound('CMS.USER.NOT_FOUND');
		}

		const updatedUserData = await UserService.updateUserStatus({
			status: req.body.status,
			condition: { id: req?.body?.id }
		});

		if (updatedUserData) {
			return res.handler.success('USER.STATUS.UPDATED');
		}
	}),

	add: asyncHandler(async (req, res) => {
		const userData = await UserService.checkUserExist(req.body.email);

		if (userData) {
			return res.handler.notFound('CMS.USER.ALREADY_EXIST')
		}

		const createdUserData = await UserService.createUser(req.body)
		return res.handler.success('CMS.USER.CREATED_SUCCESSFULLY', createdUserData);
	}),

	progressData: asyncHandler(async (req, res) => {
		let progressData = await UserService.getProgressData(req.body);

		// Check if breathWorkProgresses exists and is an array
		if (progressData && Array.isArray(progressData)) {
			const result = getBreathWorkProgressDataDayWise(progressData);

			// Update breathWorkProgresses with the new result
			progressData = result;
		}
		if (progressData) {
			return res.handler.success('CMS.USER.DETAILS_FETCHED', progressData);
		}
		return res.handler.notFound('CMS.USER.NOT_FOUND');
	}),

	delete: asyncHandler(async (req, res) => {
		let deletedUser = await UserService.userDelete(req.query.id)
		if (deletedUser) {
			return res.handler.success('CMS.USER.DELETED_SUCCESSFULLY', deletedUser);
		}
		return res.handler.notFound('CMS.USER.NOT_FOUND');
	})
}