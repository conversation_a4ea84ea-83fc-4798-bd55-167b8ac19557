const { asyncHandler } = require("../../Utils/asyncHandler");
const ContactUsService = require("../../Services/App/ContactUs.Service");


module.exports = {
    addContactUsData: asyncHandler(async (req, res) => {
        const addedData = await ContactUsService.add(req.body);

        if (addedData) {
            return res.handler.success("APP.CONTACT_US.ADDED_SUCCESSFULLY", addedData)
        }

        return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    })
}