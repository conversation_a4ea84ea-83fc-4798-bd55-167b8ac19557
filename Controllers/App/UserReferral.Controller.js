const { asyncHandler } = require("../../Utils/asyncHandler");
const UserReferralService = require("../../Services/App/UserReferral.Service");

module.exports = {
  getUserReferrals: asyncHandler(async (req, res) => {
    const userId = req.userId;
    const { month, year } = req.query;

    const referralData = await UserReferralService.getUserReferrals(userId, {
      month: month ? parseInt(month) : undefined,
      year: year ? parseInt(year) : undefined,
    });

    if (referralData) {
      return res.handler.success(
        "APP.USER_REFERRAL.LIST_FETCHED",
        referralData
      );
    }

    return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
  }),

  getUserReferralStats: asyncHandler(async (req, res) => {
    const userId = req.userId;
    const { year } = req.body;

    const statsData = await UserReferralService.getUserReferralStats(
      userId,
      year
    );

    if (statsData) {
      return res.handler.success("APP.USER_REFERRAL.STATS_FETCHED", statsData);
    }

    return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
  }),

  getLatestReferrals: asyncHandler(async (req, res) => {
    const userId = req.userId;
    const limit = 5; // Fixed limit for latest referrals

    const latestReferrals = await UserReferralService.getLatestReferrals(
      userId,
      limit
    );

    if (latestReferrals) {
      return res.handler.success(
        "APP.USER_REFERRAL.LATEST_FETCHED",
        latestReferrals
      );
    }

    return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
  }),
};
