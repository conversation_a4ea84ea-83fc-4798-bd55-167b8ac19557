const AuthService = require("../../Services/App/Auth.Service");
const UserService = require("../../Services/App/User.Service");
const ReferralHistoryService = require("../../Services/App/ReferralHistory.Service");
const { asyncHandler } = require("../../Utils/asyncHandler");

module.exports = {
  getProfile: asyncHandler(async (req, res) => {
    const userId = req?.userId;
    if (userId) {
      const userData = await AuthService.getUserDetailsByCondition({
        condition: { id: userId },
      });
      if (userData) {
        return res.handler.success("USER.AUTH.PROFILE_GET", userData);
      }
      return res.handler.badRequest("USER.AUTH.NO_DETAILS_FOUND");
    }
  }),

  userAudioPreference: asyncHandler(async (req, res) => {
    const userId = req?.userId;
    if (userId) {
      const userData = await AuthService.getUserDetailsByCondition({
        condition: { id: userId },
      });
      if (!userData) {
        return res.handler.unauthorized("USER.AUTH.USER_NOT_FOUND");
      }

      const updatedData = await UserService.updateAudioPreference(
        userId,
        req.body.audioPreference
      );
      if (updatedData) {
        return res.handler.success(
          "USER.AUDIO_PREFERENCE.UPDATED_SUCCESSFULLY"
        );
      }
      return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }
  }),

  userMusicPreference: asyncHandler(async (req, res) => {
    const userId = req?.userId;
    if (userId) {
      const userData = await AuthService.getUserDetailsByCondition({
        condition: { id: userId },
      });
      if (!userData) {
        return res.handler.unauthorized("USER.AUTH.USER_NOT_FOUND");
      }

      const updatedData = await UserService.updateMusicPreference(
        userId,
        req.body.musicPreference
      );
      if (updatedData) {
        return res.handler.success(
          "USER.AUDIO_PREFERENCE.UPDATED_SUCCESSFULLY"
        );
      }
      return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }
  }),

  userAim: asyncHandler(async (req, res) => {
    const userId = req?.userId;
    if (userId) {
      const userData = await AuthService.getUserDetailsByCondition({
        condition: { id: userId },
      });
      if (!userData) {
        return res.handler.unauthorized("USER.AUTH.USER_NOT_FOUND");
      }

      const updatedData = await UserService.updateAim(userId, req.body.id);
      if (updatedData) {
        return res.handler.success("USER.AIM.UPDATED_SUCCESSFULLY");
      }
      return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }
  }),

  updateUser: asyncHandler(async (req, res) => {
    const userId = req?.userId;
    if (userId) {
      const userData = await AuthService.getUserDetailsByCondition({
        condition: { id: userId },
      });
      if (!userData) {
        return res.handler.unauthorized("USER.AUTH.USER_NOT_FOUND");
      }

      const updatedData = await AuthService.updateUser(userId, req.body);
      if (updatedData) {
        const userData = await AuthService.getUserDetailsByCondition({
          condition: { id: userId },
        });
        if (userData) {
          return res.handler.success(
            "USER.PROFILE.UPDATED_SUCCESSFULLY",
            userData
          );
        }
        return res.handler.badRequest("USER.AUTH.NO_DETAILS_FOUND");
      }
      return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }
  }),

  getReferralInfo: asyncHandler(async (req, res) => {
    const userId = req.userId;

    // Get user's referral code
    const userData = await AuthService.getUserDetailsByCondition({
      condition: { id: userId },
    });

    if (!userData) {
      return res.handler.notFound("USER.AUTH.USER_NOT_FOUND");
    }

    // Get referral statistics
    const referralStats = await ReferralHistoryService.getUserReferralStats(
      userId
    );

    // Get referral statistics with status breakdown
    const referralStatsWithStatus =
      await ReferralHistoryService.getReferralStatsWithStatus(userId);

    // Get how user was referred
    const referredBy = await ReferralHistoryService.getUserReferredBy(userId);

    const response = {
      referralCode: userData.referralCode,
      totalReferrals: referralStats.totalReferrals,
      recentReferrals: referralStats.recentReferrals,
      statusBreakdown: referralStatsWithStatus,
      referredBy: referredBy
        ? {
            referrer: referredBy.referrer,
            referralCodeUsed: referredBy.referralCodeUsed,
            referredAt: referredBy.createdAt,
          }
        : null,
    };

    return res.handler.success("USER.REFERRAL.INFO_FETCHED", response);
  }),

  getReferralHistory: asyncHandler(async (req, res) => {
    const userId = req.userId;

    const referrals = await ReferralHistoryService.getUserReferrals(userId);

    return res.handler.success("USER.REFERRAL.HISTORY_FETCHED", referrals);
  }),

  updateUpiId: asyncHandler(async (req, res) => {
    const userId = req.userId;
    const { upiId } = req.body;

    const userData = await AuthService.getUserDetailsByCondition({
      condition: { id: userId },
    });

    if (!userData) {
      return res.handler.notFound("USER.AUTH.USER_NOT_FOUND");
    }

    const updatedData = await UserService.updateUpiId(userId, upiId);

    if (updatedData) {
      return res.handler.success("USER.UPI_ID.UPDATED_SUCCESSFULLY", {
        upiId: upiId,
      });
    }

    return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
  }),
};
