const { asyncHandler } = require("../../Utils/asyncHandler");
const AudioService = require("../../Services/App/Audio.Service");

module.exports = {
    listAudio: asyncHandler(async (req, res) => {
        const audioList = await AudioService.getList(req.body)

        if (audioList) {
            return res.handler.success("APP.AUDIO.FETCHED_SUCCESSFULLY", audioList)
        }
        return res.handler.badRequest("APP.AUDIO.NOT_FOUND")

    })
}