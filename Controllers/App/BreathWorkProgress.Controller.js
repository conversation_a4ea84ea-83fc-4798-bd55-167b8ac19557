const { asyncHandler } = require("../../Utils/asyncHandler");
const BreathWorkProgressService = require("../../Services/App/BreathWorkProgress.Service");
const { getBreathWorkProgressDataDayWise } = require("../../Utils/helpers");

module.exports = {
    addProgress: asyncHandler(async (req, res) => {
        const progressData = await BreathWorkProgressService.addProgress(req.body.duration, req.userId);

        if (progressData) {
            return res.handler.success("APP.PROGRESS.ADDED_SUCCESSFULLY", progressData)
        }
        return res.handler.badRequest("GENERAL.SOMETHING_WRONG")
    }),

    listProgress: asyncHandler(async (req, res) => {
        const progressData = await BreathWorkProgressService.listProgress(req.userId);

        if (progressData) {
            const result = getBreathWorkProgressDataDayWise(progressData)

            return res.handler.success("APP.PROGRESS.FETCHED_SUCCESSFULLY", result);
        }

        return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }),


}