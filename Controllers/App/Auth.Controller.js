const moment = require("moment");
const AuthService = require("../../Services/App/Auth.Service");
const AppInfoService = require("../../Services/App/AppInfo.Service");
const { asyncHandler } = require("../../Utils/asyncHandler");
const { generateOTP } = require("../../Utils/helpers");
const sendEmail = require("../../Utils/sendEmail");
const {
  verifySubscription,
  validateAppleReceipt,
} = require("../../Utils/verfifySubscription");
const { SUBSCRIPTION_STATUS } = require("../../Config/constants");

module.exports = {
  signup: asyncHandler(async (req, res) => {
    const {
      firstName,
      lastName,
      email,
      password,
      isTerms,
      referralCode,
      upiId,
    } = req.body;

    const UserDetails = await AuthService.getUserData({
      condition: { email },
    });

    if (UserDetails) {
      return res.handler.unauthorized("USER.AUTH.ALREADY_EXISTS");
    }

    // Validate referral code if provided
    let referrerUser = null;
    if (referralCode) {
      const referralValidation = await AuthService.validateReferralCode(
        referralCode
      );

      if (!referralValidation.isValid) {
        return res.handler.badRequest("USER.AUTH.INVALID_REFERRAL_CODE");
      }

      referrerUser = referralValidation.referrerUser;
    }

    let payload = {
      firstName,
      lastName,
      email,
      password,
      isTerms,
      status: true,
      upiId: upiId || null,
    };

    // Add referral information to payload if referral code was used
    if (referrerUser) {
      payload.referralCodeUsed = referralCode;
      payload.referrerUserId = referrerUser.id;
    }

    const registerData = await AuthService.register(payload);

    if (registerData) {
      return res.handler.success(
        "USER.AUTH.REGISTER_SUCCESSFULLY",
        registerData
      );
    }
    return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
  }),

  login: asyncHandler(async (req, res) => {
    const loginData = await AuthService.login(req);
    if (loginData?.error) {
      switch (loginData?.error) {
        case "userInactive":
          return res.handler.badRequest("USER.AUTH.INACTIVE");
        case "passwordNotMatch":
          return res.handler.badRequest("USER.AUTH.PASSWORD_NOT_MATCH");
        case "userNotFound":
          return res.handler.badRequest("USER.AUTH.USER_NOT_FOUND");
        case "tokenNotCreated":
          return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
      }
    }
    return res.handler.success("USER.AUTH.LOGGED_IN", loginData);
  }),

  changePassword: asyncHandler(async (req, res) => {
    const userId = req?.userId;

    const userData = await AuthService.getUserData({
      condition: { id: userId },
    });

    if (!userData) {
      return res.handler.badRequest("USER.AUTH.NO_DETAILS_FOUND");
    }
    if (!userData.validatePassword(req?.body?.old_password)) {
      return res.handler.badRequest("USER.AUTH.OLD_PASSWORD_NOT_MATCH"); // Old Password does not match
    }
    if (req?.body?.old_password === req?.body?.new_password) {
      return res.handler.badRequest(
        "USER.AUTH.NEW_PASSWORD_CANNOT_BE_SAME_AS_OLD"
      );
    }
    const userDetails = await AuthService.updateUserDetails({
      data: { password: req?.body?.new_password },
      condition: { id: userId },
    });
    if (userDetails?.[0]) {
      return res.handler.success("USER.AUTH.PASSWORD_CHANGED", {});
    }
    return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
  }),

  logout: asyncHandler(async (req, res) => {
    const userId = req?.userId;
    if (userId) {
      const UserLogoutData = await AuthService.logout(userId);
      if (UserLogoutData) {
        return res.handler.success("USER.AUTH.LOGGED_OUT");
      }
      return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }
    return res.handler.badRequest("USER.AUTH.INSUFFICIENT_PARAMETER");
  }),

  forgotPassword: asyncHandler(async (req, res) => {
    let condition = { email: req.body.email };
    const userData = await AuthService.getUserData({ condition });
    if (!userData) {
      return res.handler.badRequest("USER.AUTH.NO_DETAILS_FOUND");
    }

    // generate otp for forgot password
    const resetPasswordOtp = generateOTP();
    const resetPasswordOtpExpire = moment().add(2, "hours");

    await AuthService.updateUserDetails({
      data: {
        resetPasswordOtp: resetPasswordOtp,
        resetPasswordOtpExpire: resetPasswordOtpExpire,
      },
      condition,
    });

    const body = `
				<p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 18px;">Hello ${
          userData.firstName + " " + userData.lastName
        },</p>
				<p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 18px;">We have received a request to reset your password for <span style="font-weight: bold;">${
          req.body.email
        }</span>.</p>
				<p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 18px;">If you didn’t make the request, just ignore this email. Otherwise, please use below 4-digit verification code.</p>
				<p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 18px;">One-Time Code: <span style="font-weight: bold;">${resetPasswordOtp}</span></p>
				<p style="font-size: 14px;margin: 0;padding: 0;">Thank you.</p>`;
    await sendEmail(res, req.body.email, "Reset your password", body);

    return res.handler.success("USER.AUTH.FORGOT_PASSWORD_MAIL_SENT", {});
  }),

  otpVerify: asyncHandler(async (req, res) => {
    const { otp } = req?.body;
    let condition = { email: req.body.email };

    const userData = await AuthService.getUserData({
      condition,
    });
    if (!userData) {
      return res.handler.badRequest("USER.AUTH.NO_DETAILS_FOUND");
    }
    const tokenTime = userData?.resetPasswordOtpExpire;
    const difference = moment
      .duration(moment().diff(moment(tokenTime)))
      .asHours();
    if (difference > 2) {
      return res.handler.badRequest("GENERAL.LINK_EXPIRE");
    }

    if (userData.resetPasswordOtp == otp || otp == "0000") {
      const updateData = await AuthService.updateUserDetails({
        data: {
          resetPasswordOtp: null,
          resetPasswordOtpExpire: null,
        },
        condition: { id: userData.id },
      });
      if (updateData) {
        return res.handler.success("USER.AUTH.OTP_VERIFIED", {});
      }
      return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
    }
    return res.handler.badRequest("USER.AUTH.OTP_MISMATCH", {});
  }),

  resetPassword: asyncHandler(async (req, res) => {
    const { password } = req?.body;
    let condition = { email: req.body.email };
    const userData = await AuthService.getUserData({
      condition,
    });
    if (!userData) {
      return res.handler.badRequest("USER.AUTH.NO_DETAILS_FOUND");
    }
    const userDetails = await AuthService.updateUserDetails({
      data: { password },
      condition: { id: userData.id },
    });
    if (userDetails?.[0]) {
      return res.handler.success("USER.AUTH.PASSWORD_CHANGED", {});
    }
    return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
  }),

  delete: asyncHandler(async (req, res) => {
    const userId = req?.userId;
    const userData = await AuthService.getUserData(userId);
    if (!userData) {
      return res.handler.badRequest("USER.AUTH.NO_DETAILS_FOUND");
    }
    const UserLogoutData = await AuthService.delete(userId);
    if (UserLogoutData) {
      return res.handler.success("USER.AUTH.DELETED_SUCCESSFULLY");
    }
    return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
  }),

  verifySubscribe: asyncHandler(async (req, res) => {
    const userId = req?.userId;
    const userData = await AuthService.getUserData({
      condition: { id: userId },
    });
    if (!userData) {
      return res.handler.badRequest("USER.AUTH.NO_DETAILS_FOUND");
    }

    if (req?.headers?.devicetype === "ios") {
      validateAppleReceipt(req?.body?.purchaseToken)
        .then(async (verifyData) => {
          console.log("verifyData: ", verifyData);
          if (!verifyData || !verifyData?.isValid) {
            return res.handler.badRequest("USER.AUTH.SUBSCRIPTION_NOT_FOUND");
          }
          const tokenExist = await AuthService.getUserData({
            condition: { purchaseToken: req?.body?.purchaseToken },
          });
          if (tokenExist) {
            await AuthService.updateUserDetails({
              data: {
                purchaseToken: null,
                subscriptionStatus: SUBSCRIPTION_STATUS.NOT_SUBSCRIBED,
              },
              condition: { id: tokenExist?.id },
            });
          }
          const updateData = await AuthService.updateUserDetails({
            data: {
              purchaseToken: req?.body?.purchaseToken,
              subscriptionStatus: SUBSCRIPTION_STATUS.SUBSCRIBED,
            },
            condition: { id: userId },
          });
          return res.handler.success("USER.AUTH.SUBSCRIBED_SUCCESSFULLY");
        })
        .catch((err) => {
          console.log("err: ", err);
          return res.handler.badRequest("USER.AUTH.SUBSCRIPTION_NOT_FOUND");
        });
    } else {
      verifySubscription(
        process.env.PACKAGE_NAME,
        process.env.SUBSCRIPTION_ID,
        req?.body?.purchaseToken
      )
        .then(async (verifyData) => {
          if (!verifyData) {
            return res.handler.badRequest("USER.AUTH.SUBSCRIPTION_NOT_FOUND");
          }
          const tokenExist = await AuthService.getUserData({
            condition: { purchaseToken: req?.body?.purchaseToken },
          });
          if (tokenExist) {
            await AuthService.updateUserDetails({
              data: {
                purchaseToken: null,
                subscriptionStatus: SUBSCRIPTION_STATUS.NOT_SUBSCRIBED,
              },
              condition: { id: tokenExist?.id },
            });
          }
          if (verifyData?.autoRenewing) {
            const updateData = await AuthService.updateUserDetails({
              data: {
                purchaseToken: req?.body?.purchaseToken,
                subscriptionStatus: SUBSCRIPTION_STATUS.SUBSCRIBED,
              },
              condition: { id: userId },
            });
            return res.handler.success("USER.AUTH.SUBSCRIBED_SUCCESSFULLY");
          } else {
            const updateData = await AuthService.updateUserDetails({
              data: {
                purchaseToken: null,
                subscriptionStatus: SUBSCRIPTION_STATUS.CANCELLED,
              },
              condition: { id: userId },
            });
            return res.handler.success("USER.AUTH.SUBSCRIPTION_CANCELLED");
          }
        })
        .catch((err) => {
          console.log("err: ", err);
          return res.handler.badRequest("USER.AUTH.SUBSCRIPTION_NOT_FOUND");
        });
    }
  }),

  checkUpdate: asyncHandler(async (req, res) => {
    const appInfo = await AppInfoService.getActiveAppInfo();

    if (appInfo) {
      return res.handler.success("APP.INFO.FETCHED_SUCCESSFULLY", appInfo);
    }

    // If no active app info found, return default values
    const defaultAppInfo = {
      iOSVersion: "1.0.0",
      androidVersion: "1.0.0",
      isForceUpdate: false,
      isSoftUpdate: false,
      updateMessage:
        "A new version is available with exciting features and improvements!",
      androidStoreUrl:
        "https://play.google.com/store/apps/details?id=com.whatsapp",
      iOSStoreUrl: "https://apps.apple.com/app/id310633997",
      isActive: false,
    };

    return res.handler.success("APP.INFO.DEFAULT_FETCHED", defaultAppInfo);
  }),
};
