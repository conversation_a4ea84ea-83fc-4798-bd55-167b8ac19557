const { asyncHandler } = require("../../Utils/asyncHandler");
const StaticPageService = require("../../Services/App/Static-page.Service");

module.exports = {
    getDetails: asyncHandler(async (req, res) => {
        const details = await StaticPageService.getPageData(req.query.id);

        if (details) {
            return res.handler.success("APP.STATIC_PAGE.FETCHED_SUCCESSULLY", details)
        }

        return res.handler.badRequest("APP.STATIC_PAGE.NOT_FOUND");
    })
}