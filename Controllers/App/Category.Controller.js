const CategoryService = require("../../Services/App/Category.Service");
const { asyncHandler } = require("../../Utils/asyncHandler");

module.exports = {
  categoryDetail: asyncHandler(async (req, res) => {
    const name = req.query.categoryName;

    const categoryDetails = await CategoryService.getCategoryData(name);

    if (!categoryDetails) {
      return res.handler.badRequest("APP.CATEGORY.NOT_FOUND");
    }

    return res.handler.success(
      "APP.CATEGORY.FETCHED_SUCCESSFYLLY",
      categoryDetails
    );
  }),

  getVideo: asyncHandler(async (req, res) => {
    const videoDetails = await CategoryService.getVideoData(req.query.id);

    if (!videoDetails) {
      return res.handler.badRequest("APP.CATEGORY.VIDEO_NOT_FOUND");
    }

    return res.handler.success(
      "APP.PLAYLIST.FETCHED_SUCCESSFYLLY",
      videoDetails
    );
  }),

  aimList: asyncHandler(async (req, res) => {
    const aimList = await CategoryService.getAimListData();

    if (!aimList) {
      return res.handler.badRequest("APP.CATEGORY.AIM_NOT_FOUND");
    }
    return res.handler.success(
      "APP.CATEGORY.AIM_LIST_FETCHED_SUCCESSFYLLY",
      aimList
    );
  }),
  getRecommendedCategories: asyncHandler(async (req, res) => {
    const id = req.query.aim;
    const categories = await CategoryService.getRecommendedCategories(id);

    if (!categories) {
      return res.handler.badRequest("APP.CATEGORY.NOT_FOUND");
    }

    return res.handler.success("APP.CATEGORY.FETCHED_SUCCESSFYLLY", categories);
  }),
  getRecommendedCategoriesList: asyncHandler(async (req, res) => {
    const categories = await CategoryService.getRecommendedCategoriesList();

    if (!categories) {
      return res.handler.badRequest("APP.CATEGORY.NOT_FOUND");
    }

    return res.handler.success("APP.CATEGORY.FETCHED_SUCCESSFYLLY", categories);
  }),

  addVideoActivity: asyncHandler(async (req, res) => {
    const videoActivity = await CategoryService.addVideoActivity(
      req.userId,
      req.body
    );

    if (videoActivity) {
      return res.handler.success("APP.CATEGORY.VIDEO_ACTIVITY_ADDED");
    }

    return res.handler.badRequest("GENERAL.SOMETHING_WRONG");
  }),
};
