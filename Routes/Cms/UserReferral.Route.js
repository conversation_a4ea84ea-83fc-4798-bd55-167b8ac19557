const express = require("express");
const router = express.Router();
const Validator = require("../../Middleware/Validator");
const AdminAuthorization = require("../../Middleware/AdminAuth");
const { uploadImage } = require("../../Middleware/Multer");
const {
  addUserReferralValidation,
  listUserReferralValidation,
} = require("../../Validations/Cms/UserReferral.Validation");
const UserReferralController = require("../../Controllers/Cms/UserReferral.Controller");

router.post(
  "/add",
  uploadImage.single("image"),
  AdminAuthorization,
  Validator(addUserReferralValidation),
  UserReferralController.add
);

router.post(
  "/list",
  AdminAuthorization,
  Validator(listUserReferralValidation),
  UserReferralController.list
);

module.exports = router;
