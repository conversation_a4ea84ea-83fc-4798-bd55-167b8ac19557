const express = require("express");
const router = express.Router();

const AdminAuthorization = require("../../Middleware/AdminAuth");
const { uploadImage } = require("../../Middleware/Multer");
const Validator = require("../../Middleware/Validator");
const { ImageValidation } = require("../../Validations/Cms/HomePage.Validation");
const HomeController = require("../../Controllers/Cms/Home.Controller");

router.post("/upload-image", uploadImage.single('bannerImage'), AdminAuthorization, HomeController.add)
router.post("/list", AdminAuthorization, Validator(ImageValidation), HomeController.list)

module.exports = router;