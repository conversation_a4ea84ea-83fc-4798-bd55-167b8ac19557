const express = require("express");
const router = express.Router();

const AdminAuthorization = require("../../Middleware/AdminAuth");
const Validator = require("../../Middleware/Validator");
const { uploadImage } = require("../../Middleware/Multer");
const { addAudioValidation, listAudioValidation, deleteAudioValidation, editAudioValidation, audioDetailsValidation } = require("../../Validations/Cms/Audio.Validation");
const AudioController = require('../../Controllers/Cms/Audio.Controller');

router.post("/add", uploadImage.single('file'), AdminAuthorization, Validator(addAudioValidation), AudioController.add);
router.post("/list", AdminAuthorization, Validator(listAudioValidation), AudioController.list);
router.post(
    "/details",
    AdminAuthorization,
    Validator(audioDetailsValidation),
    AudioController?.audioDetails
);
router.delete("/delete", AdminAuthorization, Validator(deleteAudioValidation), AudioController?.delete);
router.put(
    '/edit',
    uploadImage.single('file'),
    AdminAuthorization,
    Validator(editAudioValidation),
    AudioController?.edit
);

module.exports = router;