var express = require("express");
var router = express.Router();
const Validator = require('../../Middleware/Validator');

const CategoryController = require('../../Controllers/Cms/Category.Controller');
const {
	createCategoryValidation,
	listCategoryValidation,
	categoryDetailsValidation,
	addPlaylistValidation,
	getPlaylistValidation,
	editCategoryValidation,
	categoryDeleteValidation,
	getRecommendationValidation,
	listRecommendationValidation,
	deletePlaylistValidation
} = require('../../Validations/Cms/Category.Validation');

const AdminAuthorization = require("../../Middleware/AdminAuth")
const { uploadImage } = require('../../Middleware/Multer');

router.post('/add', uploadImage.single('bannerImage'), AdminAuthorization, Validator(createCategoryValidation), CategoryController?.add);
router.post('/list', AdminAuthorization, Validator(listCategoryValidation), CategoryController?.list);
router.post(
	'/details',
	AdminAuthorization,
	Validator(categoryDetailsValidation),
	CategoryController?.categoryDetails
);
router.put(
	'/edit',
	uploadImage.single('bannerImage'),
	AdminAuthorization,
	Validator(editCategoryValidation),
	CategoryController?.edit
);
router.post('/add-playlist', uploadImage.fields([
	{ name: 'banner', maxCount: 1 },
	{ name: 'file', maxCount: 1 }
]), AdminAuthorization, Validator(addPlaylistValidation), CategoryController?.addPlaylist);
router.post(
	'/get-playlist',
	AdminAuthorization,
	Validator(getPlaylistValidation),
	CategoryController?.getPlaylist
);
router.delete('/delete-playlist', AdminAuthorization, Validator(deletePlaylistValidation), CategoryController?.deletePlaylist);
router.delete('/delete', AdminAuthorization, Validator(categoryDeleteValidation), CategoryController?.delete);
router.post('/list-have-parent', AdminAuthorization, CategoryController?.listHaveParent);
router.post('/add-recommendation', AdminAuthorization, CategoryController?.addRecommendation)
router.post('/get-recommendation-details', AdminAuthorization, Validator(getRecommendationValidation), CategoryController?.getRecommendationDetails)
router.post('/list-recommendation', AdminAuthorization, Validator(listRecommendationValidation), CategoryController?.listRecommendation)

module.exports = router;