const express = require('express');
const router = express.Router();
const Validator = require('../../Middleware/Validator');
const AdminAuthorization = require('../../Middleware/AdminAuth');
const {
	listUserValidation,
	userDetailsValidation,
	editUserDetailsValidation,
	updateStatusValidation,
	addUserValidation,
	progressDataValidation,
	deleteUserValidation
} = require('../../Validations/Cms/User.Validation');
const UserController = require('../../Controllers/Cms/User.Controller');
const { uploadImage } = require('../../Middleware/Multer');


router.post(
	'/add',
	uploadImage.single('avatar'),
	AdminAuthorization,
	Validator(addUserValidation),
	UserController?.add
);

router.post(
	'/list',
	AdminAuthorization,
	Validator(listUserValidation),
	UserController?.list
);
router.post(
	'/details',
	AdminAuthorization,
	Validator(userDetailsValidation),
	UserController?.userDetails
);
router.put(
	'/edit',
	uploadImage.single('avatar'),
	AdminAuthorization,
	Validator(editUserDetailsValidation),
	UserController?.editUserDetails
);
router.put(
	'/update-status',
	AdminAuthorization,
	Validator(updateStatusValidation),
	UserController?.updateUserStatus
);

router.post(
	'/progress-data',
	AdminAuthorization,
	Validator(progressDataValidation),
	UserController?.progressData
);

router.delete(
	'/delete',
	AdminAuthorization,
	Validator(deleteUserValidation),
	UserController?.delete
);

module.exports = router;
