/*
 * Summary:     index.js file for handling all routes, request and response for admin panel - (CMS related actions).
 * Author:      Openxcell
 */

/**require NPM-modules for configuration */
var express = require("express");
var router = express.Router();

/* require for Authentication */
const AuthRoute = require("./Auth.Route");
const StaticPageRoute = require("./StaticPage.Route");
const UserRoute = require("./User.Route");
const CategoryRoute = require("./Category.Route");
const HomeRoute = require("./HomePage.Route");
const AudioRoute = require("./Audio.Route");
const ContactUsRoute = require("./ContactUs.Route");
const DashboardRoute = require("./Dashboard.Route");
const UploadRoute = require("./Upload.Route");
const UserReferralRoute = require("./UserReferral.Route");

/* routes of admin panel*/
router.use("/admin", AuthRoute);
router.use("/static-page", StaticPageRoute);
router.use("/user", UserRoute);
router.use("/category", CategoryRoute);
router.use("/home", HomeRoute);
router.use("/audio", AudioRoute);
router.use("/contact-us", ContactUsRoute);
router.use("/dashboard", DashboardRoute);
router.use("/uploads", UploadRoute);
router.use("/user-referral", UserReferralRoute);

module.exports = router;
