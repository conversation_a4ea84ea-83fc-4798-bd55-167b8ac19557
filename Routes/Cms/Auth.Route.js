var express = require("express");
var router = express.Router();
const Validator = require('../../Middleware/Validator');

const AuthController = require('../../Controllers/Cms/Auth.Controller');
const {
	loginValidation,
	editProfileValidation,
	changePasswordValidation,
	forgotPasswordValidation,
	verifyTokenValidation,
	resetPasswordValidation
} = require('../../Validations/Cms/Auth.Validation');

const AdminAuthorization = require("../../Middleware/AdminAuth")
const { uploadImage } = require('../../Middleware/Multer');

router.post('/login', Validator(loginValidation), AuthController.login);
router.get('/get-profile', AdminAuthorization, AuthController?.getProfile);
router.delete('/logout', AdminAuthorization, AuthController?.logout);
router.put(
	'/update-profile',
	AdminAuthorization,
	uploadImage.single('profileImage'),
	Validator(editProfileValidation),
	AuthController.updateProfile
);
router.put(
	'/change-password',
	AdminAuthorization,
	Validator(changePasswordValidation),
	AuthController.changePassword
);
router.post('/forgot-password', Validator(forgotPasswordValidation), AuthController.forgotPassword);
router.post('/verify-token', Validator(verifyTokenValidation), AuthController.verifyToken);
router.post('/reset-password/:userToken', Validator(resetPasswordValidation), AuthController.resetPassword);


module.exports = router;