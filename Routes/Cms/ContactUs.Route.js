const express = require("express");
const router = express.Router();

const AdminAuthorization = require("../../Middleware/AdminAuth");
const { ContactUsListValidation, contactUsDetailsValidation, replyContactUsValidation, deleteContactUsValidation } = require("../../Validations/Cms/ContactUs.Validation");
const ContactUsController = require("../../Controllers/Cms/ContactUs.Controller");
const Validator = require("../../Middleware/Validator");

router.post("/list", AdminAuthorization, Validator(ContactUsListValidation), ContactUsController.list)
router.post("/details", AdminAuthorization, Validator(contactUsDetailsValidation), ContactUsController.contactUsDetails)
router.post("/reply", AdminAuthorization, Validator(replyContactUsValidation), ContactUsController.reply)
router.delete('/delete', AdminAuthorization, Validator(deleteContactUsValidation), ContactUsController?.delete);

module.exports = router;