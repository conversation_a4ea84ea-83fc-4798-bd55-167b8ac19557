const express = require("express");
const AdminAuthorization = require("../../Middleware/AdminAuth");
const DashboardController = require("../../Controllers/Cms/Dashboard.Contoller");
const router = express.Router();

router.get("/stats", AdminAuthorization, DashboardController.getStats);
router.get(
  "/get-user-stat",
  AdminAuthorization,
  DashboardController.getUserStat
);
router.get(
  "/breathwork-progress-stat",
  AdminAuthorization,
  DashboardController.getBreatworkProgressStat
);
router.get(
  "/subscription-status",
  AdminAuthorization,
  DashboardController.getSubscriptionStatusStats
);

module.exports = router;
