const express = require("express");
const router = express.Router();
const Validator = require("../../Middleware/Validator");
const AdminAuthorization = require("../../Middleware/AdminAuth");
const {
  editPageValidation,
  addStaticPageValidation,
  staticPageListValidation,
  staticPageDetailsValidation,
  staticPageDeleteValidation,
} = require("../../Validations/Cms/StaticPage.Validation");
const PageController = require("../../Controllers/Cms/StaticPage.Controller");

router.post(
  "/add",
  AdminAuthorization,
  Validator(addStaticPageValidation),
  PageController?.add
);
router.post(
  "/list",
  AdminAuthorization,
  Validator(staticPageListValidation),
  PageController?.list
);
router.post(
  "/details",
  Validator(staticPageDetailsValidation),
  PageController?.pageDetails
);
router.put(
  "/edit",
  AdminAuthorization,
  Validator(editPageValidation),
  PageController?.editPage
);
router.delete(
  "/delete",
  AdminAuthorization,
  Validator(staticPageDeleteValidation),
  PageController?.delete
);

module.exports = router;
