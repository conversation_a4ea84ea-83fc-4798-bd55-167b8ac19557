var express = require("express");
var router = express.Router();


/* require for Controller */
const { UploadController } = require("../../Controllers/Cms/Upload.Controller");

/* Routes of Multipart upload panel */

router.post("/initializeMultipartUpload", UploadController.initializeMultipartUpload)

router.post("/getMultipartPreSignedUrls", UploadController.getMultipartPreSignedUrls)

router.post("/finalizeMultipartUpload", UploadController.finalizeMultipartUpload)

module.exports = router;
