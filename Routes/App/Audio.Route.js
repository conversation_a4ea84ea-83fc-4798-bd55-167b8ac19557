const express = require("express");
const Validator = require("../../Middleware/Validator");
const { listAudioValidation } = require("../../Validations/App/Audio.Validation");
const AudioController = require("../../Controllers/App/Audio.Controller");
const UserAuthorization = require("../../Middleware/UserAuth");
const router = express.Router();

router.post("/list", UserAuthorization, AudioController.listAudio)

module.exports = router;