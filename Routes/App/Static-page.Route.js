const express = require("express");
const Validator = require("../../Middleware/Validator");
const StaticPageController = require("../../Controllers/App/Static-page.Controller");
const UserAuthorization = require("../../Middleware/UserAuth");
const { getStaticPageValidation } = require("../../Validations/App/Static-page.Validation");
const router = express.Router();

router.get("/details", Validator(getStaticPageValidation), StaticPageController.getDetails)

module.exports = router;

