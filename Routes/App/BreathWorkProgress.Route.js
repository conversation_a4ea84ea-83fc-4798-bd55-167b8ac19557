const express = require("express");
const UserAuthorization = require("../../Middleware/UserAuth");
const Validator = require("../../Middleware/Validator");
const { addProgressValidation } = require("../../Validations/App/BreathWorkProgress.Validator");
const BreathWorkProgressController = require("../../Controllers/App/BreathWorkProgress.Controller");

const router = express.Router();

router.post("/add", UserAuthorization, Validator(addProgressValidation), BreathWorkProgressController.addProgress)
router.get("/list", UserAuthorization, BreathWorkProgressController.listProgress)

module.exports = router;