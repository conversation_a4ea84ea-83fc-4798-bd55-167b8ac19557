const express = require("express");
const UserAuthorization = require("../../Middleware/UserAuth");
const Validator = require("../../Middleware/Validator");
const { addContactUsValidation } = require("../../Validations/App/ContactUs.Validation");
const ContactUsContoller = require("../../Controllers/App/ContactUs.Controller")
const router = express.Router();

router.post("/add", UserAuthorization, Validator(addContactUsValidation), ContactUsContoller.addContactUsData)

module.exports = router;