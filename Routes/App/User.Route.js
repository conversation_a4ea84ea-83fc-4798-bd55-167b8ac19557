var express = require("express");
var router = express.Router();
const UserController = require("../../Controllers/App/User.Controller");
const UserAuthorization = require("../../Middleware/UserAuth");
const Validator = require("../../Middleware/Validator");
const {
  userAudioPreferenceValidation,
  userMusicPreferenceValidation,
  userAimValidation,
  updateUserValidation,
} = require("../../Validations/App/User.Validation");

router.get("/get-profile", UserAuthorization, UserController?.getProfile);
router.put(
  "/update-audio-preference",
  UserAuthorization,
  Validator(userAudioPreferenceValidation),
  UserController?.userAudioPreference
);
router.put(
  "/update-music-preference",
  UserAuthorization,
  Validator(userMusicPreferenceValidation),
  UserController?.userMusicPreference
);
router.put(
  "/update-aim",
  UserAuthorization,
  Validator(userAimValidation),
  UserController?.userAim
);
router.put(
  "/update",
  UserAuthorization,
  Validator(updateUserValidation),
  UserController?.updateUser
);

// Referral routes
router.get(
  "/referral-info",
  UserAuthorization,
  UserController?.getReferralInfo
);
router.get(
  "/referral-history",
  UserAuthorization,
  UserController?.getReferralHistory
);

module.exports = router;
