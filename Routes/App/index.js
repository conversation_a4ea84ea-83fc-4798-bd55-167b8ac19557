/*
 * Summary:     index.js file for handling all routes, request and response for application side.
 * Author:      Openxcell
 */

/**require NPM-modules for configuration */
var express = require("express");
var router = express.Router();

/* require for Authentication */
const AuthRoute = require("./Auth.Route");
const UserRoute = require("./User.Route");
const CategoryRoute = require("./Category.Route");
const BreathWorkProgressRoute = require("./BreathWorkProgress.Route");
const StaticPageRoute = require("./Static-page.Route");
const ContactUsRoute = require("./ContactUs.Route");
const AudioRoute = require("./Audio.Route");
const UserReferralRoute = require("./UserReferral.Route");

/* routes of app panel*/
router.use("/auth", AuthRoute);
router.use("/user", UserRoute);
router.use("/category", CategoryRoute);
router.use("/breath-work-progress", BreathWorkProgressRoute);
router.use("/static-page", StaticPageRoute);
router.use("/contact-us", ContactUsRoute);
router.use("/audio", AudioRoute);
router.use("/user-referral", UserReferralRoute);

module.exports = router;
