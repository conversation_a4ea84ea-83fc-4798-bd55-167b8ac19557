const express = require("express");
const Validator = require("../../Middleware/Validator");
const CategoryController = require("../../Controllers/App/Category.Controller");
const UserAuthorization = require("../../Middleware/UserAuth");
const { categoryDetailValidation, playlistVideoValidation, videoActivityProgressValidation } = require("../../Validations/App/Category.Validation");
const router = express.Router();

router.get("/details", UserAuthorization, Validator(categoryDetailValidation), CategoryController.categoryDetail)
router.get("/playlist-video", UserAuthorization, Validator(playlistVideoValidation), CategoryController.getVideo)
router.get("/list-aiming-category", UserAuthorization, CategoryController.aimList)
router.get("/recommended-catgories", UserAuthorization, CategoryController.getRecommendedCategories)
router.get("/recommended-catgories-list", UserAuthorization, CategoryController.getRecommendedCategoriesList)
router.post("/video-activity", UserAuthorization, Validator(videoActivityProgressValidation), CategoryController.addVideoActivity)

module.exports = router;

