const express = require("express");
const router = express.Router();
const Validator = require("../../Middleware/Validator");
const UserAuthorization = require("../../Middleware/UserAuth");
const {
  getUserReferralsValidation,
  getUserReferralStatsValidation,
} = require("../../Validations/App/UserReferral.Validation");
const UserReferralController = require("../../Controllers/App/UserReferral.Controller");

// Get user's referral list with optional filtering
router.get(
  "/list",
  UserAuthorization,
  Validator(getUserReferralsValidation),
  UserReferralController.getUserReferrals
);

// Get user's referral statistics
router.post(
  "/stats",
  UserAuthorization,
  Validator(getUserReferralStatsValidation),
  UserReferralController.getUserReferralStats
);

// Get latest referrals for the user
router.get(
  "/latest",
  UserAuthorization,
  UserReferralController.getLatestReferrals
);

module.exports = router;
