var express = require("express");
var router = express.Router();
const Validator = require("../../Middleware/Validator");
const {
  signupValidation,
  loginValidation,
  changePasswordValidation,
  forgotPasswordValidation,
  otpVerifyValidation,
  resetPasswordValidation,
  verifySubscribeValidation,
} = require("../../Validations/App/Auth.Validation");
const AuthController = require("../../Controllers/App/Auth.Controller");
const UserAuthorization = require("../../Middleware/UserAuth");

router.post("/signup", Validator(signupValidation), AuthController?.signup);
router.post("/login", Validator(loginValidation), AuthController?.login);
router.put(
  "/change-password",
  UserAuthorization,
  Validator(changePasswordValidation),
  AuthController?.changePassword
);
router.delete("/logout", UserAuthorization, AuthController?.logout);
router.post(
  "/forgot-password",
  Validator(forgotPasswordValidation),
  AuthController?.forgotPassword
);
router.post(
  "/verify-otp",
  Validator(otpVerifyValidation),
  AuthController?.otpVerify
);
router.post(
  "/reset-password",
  Validator(resetPasswordValidation),
  AuthController?.resetPassword
);
router.post(
  "/verify-subscribe",
  UserAuthorization,
  Validator(verifySubscribeValidation),
  AuthController?.verifySubscribe
);
router.delete("/delete", UserAuthorization, AuthController?.delete);

// App update check route (no authentication required)
router.get("/check-update", AuthController?.checkUpdate);

module.exports = router;
