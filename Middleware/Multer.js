const multer = require('multer');
const path = require('path');
const os = require('os');
const fs = require('fs');
const { generateRandomString } = require('../Utils/helpers');
const tmpdir = os.tmpdir();

var storage = multer.diskStorage({
	destination: function (req, file, cb) {
		cb(null, tmpdir);
	},
	filename: function (req, file, cb) {
		const filename =
			generateRandomString(5) +
			'-' +
			Date.now() +
			path.extname(file.originalname);
		file.originalname = filename;
		const filepath = path.join(tmpdir, filename);
		fs.mkdtemp(filepath, (err, folder) => {
			if (err) throw err;
			cb(null, filename);
		});
	}
});

// Check file type
function checkFileType(file, cb) {
	// Allowed extensions
	const filetypes = /jpeg|jpg|png|mp4|avi|mov|webm|mpeg|mp3|wav|aac/;
	// Check extension
	const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
	// Check MIME type
	// const mimetype = filetypes.test(file.mimetype);
	if (extname) {
		return cb(null, true);
	} else {
		cb('VALIDATION.FILE.FILE_FORMAT');
	}
}

const uploadImage = multer({
	storage: storage,
	fileFilter: function (req, file, cb) {
		checkFileType(file, cb);
	}
});
const multiFile = multer({
	storage: storage,
}).any();

module.exports = { uploadImage, multiFile };
