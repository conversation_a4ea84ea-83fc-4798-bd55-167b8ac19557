const jwt = require('jsonwebtoken');
const AdminTokenModel =
	require('../Database/Models/index').adminToken;
const { JWTTOKEN } = require('../Config/constants');

const AdminAuthorization = async (req, res, next) => {
	try {
		if (req?.headers?.authorization) {
			// verify token
			const authorization = req?.headers?.authorization?.split(' ')[1];
			if (authorization) {
				const adminDetail = await jwt.verify(
					authorization,
					JWTTOKEN.secret,
					{
						algorithm: JWTTOKEN.algorithm,
						expiresIn: JWTTOKEN.expiresIn
					}
				);
				const adminTokenExists = await AdminTokenModel.findOne({
					where: {
						accessToken: authorization,
						adminId: adminDetail?.id
					}
				});
				if (adminTokenExists) {
					req.adminId = adminDetail?.id;
					req.email = adminDetail?.email;
					next();
				} else {
					res.handler.unauthorized();
				}
			} else {
				res.handler.unauthorized('VALIDATION.TOKEN.INVALID');
			}
		} else {
			res.handler.unauthorized('VALIDATION.TOKEN.REQUIRED');
		}
	} catch (error) {
		console.log('Error', error);
		res.handler.unauthorized('VALIDATION.TOKEN.INVALID');
	}
};

module.exports = AdminAuthorization;
