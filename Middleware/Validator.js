const Validator = (validationSchema) => async (req, res, next) => {
	try {
		const parsedValues = await validationSchema?.validate({
			body: req?.body,
			query: req?.query,
			params: req?.params
		});
		req.body = parsedValues.body;
		req.query = parsedValues.query;
		req.params = parsedValues.params;
		next();
	} catch (error) {
		res.handler.badRequest(error?.message);
	}
};

module.exports = Validator;
