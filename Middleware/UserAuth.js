const jwt = require('jsonwebtoken');
const UserTokenModel =
	require('../Database/Models/index').userToken;
const UserModel =
	require('../Database/Models/index').user;
const { JWTTOKEN } = require('../Config/constants');


const UserAuthorization = async (req, res, next) => {
	try {
		if (req?.headers?.authorization) {
			const authorization = req?.headers?.authorization?.split(' ')[1];
			if (authorization) {
				// verify token
				const userDetail = await jwt.verify(
					authorization,
					JWTTOKEN.secret,
					{
						algorithm: JWTTOKEN.algorithm,
						expiresIn: JWTTOKEN.expiresIn
					}
				);
				const user = await UserModel.findOne({
					where: {
						id: userDetail?.id
					}
				})

				if (!user.status) {
					res.handler.unauthorized('The user account is inactive.');
				}

				const userTokenExists = await UserTokenModel.findOne({
					where: {
						accessToken: authorization,
						userId: userDetail?.id
					}
				});

				if (userTokenExists) {
					req.userId = userDetail?.id;
					req.email = userDetail?.email;
					next();
				} else {
					res.handler.unauthorized('VALIDATION.TOKEN.INVALID');
				}
			} else {
				res.handler.unauthorized('VALIDATION.TOKEN.INVALID');
			}
		} else {
			res.handler.unauthorized('VALIDATION.TOKEN.REQUIRED');
		}
	} catch (error) {
		res.handler.unauthorized('VALIDATION.TOKEN.INVALID');
	}
};

module.exports = UserAuthorization;