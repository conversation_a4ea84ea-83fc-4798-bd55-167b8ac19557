const { object, number } = require("yup");

module.exports = {
  getUserReferralsValidation: object({
    query: object({
      month: number()
        .nullable()
        .min(1, "VALIDATION.MONTH.MIN")
        .max(12, "VALIDATION.MONTH.MAX")
        .typeError("VALIDATION.MONTH.TYPE_ERROR"),
      year: number()
        .nullable()
        .min(2000, "VALIDATION.YEAR.MIN")
        .max(2100, "VALIDATION.YEAR.MAX")
        .typeError("VALIDATION.YEAR.TYPE_ERROR"),
    }),
  }),

  getUserReferralStatsValidation: object({
    body: object({
      year: number()
        .nullable()
        .min(2000, "VALIDATION.YEAR.MIN")
        .max(2100, "VALIDATION.YEAR.MAX")
        .typeError("VALIDATION.YEAR.TYPE_ERROR"),
    }),
  }),
};
