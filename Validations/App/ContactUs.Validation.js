const { object, string } = require("yup");

module.exports = {
    addContactUsValidation: object({
        body: object({
            userName: string()
                .trim()
                .required("VALIDATION.USERNAME.REQUIRED")
                .typeError('VALIDATION.USERNAME.TYPE_ERROR'),
            email: string()
                .trim()
                .email("VALIDATION.EMAIL.INVALID")
                .required("VALIDATION.EMAIL.REQUIRED"),
            description: string()
                .trim()
                .required("VALIDATION.DESCRIPTION.REQUIRED")
                .typeError('VALIDATION.DESCRIPTION.TYPE_ERROR'),
        })
    })
}