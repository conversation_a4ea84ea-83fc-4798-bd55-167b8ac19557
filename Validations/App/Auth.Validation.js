const { object, string, number, boolean } = require("yup");

const passwordRegex = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,15}$/;

module.exports = {
  loginValidation: object({
    body: object({
      email: string()
        .trim()
        .email("VALIDATION.EMAIL.INVALID")
        .required("VALIDATION.EMAIL.REQUIRED"),
      password: string().trim().required("VALIDATION.PASSWORD.REQUIRED"),
    }),
  }),

  signupValidation: object({
    body: object({
      email: string()
        .trim()
        .email("VALIDATION.EMAIL.INVALID")
        .required("VALIDATION.EMAIL.REQUIRED"),
      password: string()
        .trim()
        .required("VALIDATION.PASSWORD.REQUIRED")
        .matches(passwordRegex, "VALIDATION.PASSWORD.VALID"),
      firstName: string()
        .trim()
        .matches(/^[a-zA-Z0-9\s]+$/, "VALIDATION.FIRSTNAME.VALID")
        .typeError("VALIDATION.FIRSTNAME.TYPE_ERROR")
        .required("VALIDATION.FIRSTNAME.REQUIRED"),
      lastName: string()
        .trim()
        .matches(/^[a-zA-Z0-9\s]+$/, "VALIDATION.LASTNAME.VALID")
        .typeError("VALIDATION.LASTNAME.TYPE_ERROR")
        .required("VALIDATION.LASTNAME.REQUIRED"),
      isTerms: boolean()
        .typeError("VALIDATION.ISTERM.TYPE_ERROR")
        .required("VALIDATION.ISTERM.REQUIRED"),
      referralCode: string()
        .nullable()
        .trim()
        .typeError("VALIDATION.REFERRAL_CODE.TYPE_ERROR"),
      upiId: string()
        .nullable()
        .trim()
        .matches(/^[\w.-]+@[\w.-]+$/, "VALIDATION.UPI_ID.FORMAT_ERROR")
        .typeError("VALIDATION.UPI_ID.TYPE_ERROR"),
    }),
  }),

  editProfileValidation: object({
    body: object({
      firstName: string()
        .trim()
        .matches(/^[a-zA-Z0-9\s]+$/, "VALIDATION.NAME.VALID")
        .typeError("VALIDATION.NAME.TYPE_ERROR")
        .required("VALIDATION.NAME.REQUIRED"),
      lastName: string()
        .trim()
        .matches(/^[a-zA-Z0-9\s]+$/, "VALIDATION.NAME.VALID")
        .typeError("VALIDATION.NAME.TYPE_ERROR")
        .required("VALIDATION.NAME.REQUIRED"),
      id: number()
        .required("VALIDATION.ID.REQUIRED")
        .typeError("VALIDATION.ID.TYPE_ERROR"),
    }).noUnknown(true),
  }),

  changePasswordValidation: object({
    body: object({
      old_password: string()
        .trim()
        .required("VALIDATION.OLD_PASSWORD.REQUIRED"),
      new_password: string()
        .trim()
        .required("VALIDATION.NEW_PASSWORD.REQUIRED")
        .matches(passwordRegex, "VALIDATION.NEW_PASSWORD.VALID"),
    }),
  }),

  forgotPasswordValidation: object({
    body: object({
      email: string()
        .trim()
        .email("VALIDATION.EMAIL.INVALID")
        .required("VALIDATION.EMAIL.REQUIRED"),
    }),
  }),

  otpVerifyValidation: object({
    body: object({
      email: string()
        .trim()
        .email("VALIDATION.EMAIL.INVALID")
        .required("VALIDATION.EMAIL.REQUIRED"),
      otp: string()
        .trim()
        .min(4, "VALIDATION.OTP.LENGTH")
        .max(4, "VALIDATION.OTP.LENGTH")
        .required("VALIDATION.OTP.REQUIRED"),
    }),
  }),

  resetPasswordValidation: object({
    body: object({
      email: string()
        .trim()
        .email("VALIDATION.EMAIL.INVALID")
        .required("VALIDATION.EMAIL.REQUIRED"),
      password: string()
        .trim()
        .required("VALIDATION.PASSWORD.REQUIRED")
        .matches(passwordRegex, "VALIDATION.PASSWORD.VALID"),
    }),
  }),

  verifySubscribeValidation: object({
    body: object({
      purchaseToken: string()
        .trim()
        .required("VALIDATION.PURCHASE_TOKEN.REQUIRED"),
    }),
  }),
};
