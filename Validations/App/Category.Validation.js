const { string, object, number, boolean } = require("yup");

module.exports = {
    categoryDetailValidation: object({
        query: object({
            categoryName: string()
                .trim()
                .required('VALIDATION.CATEGORY.REQUIRED')
        }),
    }),

    playlistVideoValidation: object({
        query: object({
            id: number()
                .required('VALIDATION.CATEGORY.REQUIRED')
        }),
    }),

    videoActivityProgressValidation: object({
        body: object({
            type: string()
                .oneOf(['breathwork', 'athleticBreathwork'])
                .required('VALIDATION.TYPE.REQUIRED'),
            isPlay: boolean()
                .required('VALIDATION.IS_PLAY.REQUIRED'),
        })
    })
}    
