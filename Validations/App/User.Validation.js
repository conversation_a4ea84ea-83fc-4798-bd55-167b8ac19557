const { string, object } = require("yup");

module.exports = {
  userAudioPreferenceValidation: object({
    body: object({
      audioPreference: string().required("VALIDATION.AUDIO.REQUIRED"),
    }),
  }),
  userMusicPreferenceValidation: object({
    body: object({
      musicPreference: string()
        .typeError("VALIDATION.MUSIC.TYPE_ERROR")
        .required("VALIDATION.MUSIC.REQUIRED"),
    }),
  }),
  userAimValidation: object({
    body: object({
      id: string()
        .trim()
        .required("VALIDATION.ID.REQUIRED")
        .typeError("VALIDATION.ID.TYPE_ERROR"),
    }),
  }),
  updateUserValidation: object({
    body: object({
      firstName: string().required("VALIDATION.FIRSTNAME.REQUIRED"),
      lastName: string().required("VALIDATION.LASTNAME.REQUIRED"),
    }),
  }),
  updateUpiIdValidation: object({
    body: object({
      upiId: string()
        .trim()
        .required("VALIDATION.UPI_ID.REQUIRED")
        .matches(/^[\w.-]+@[\w.-]+$/, "VALIDATION.UPI_ID.FORMAT_ERROR")
        .typeError("VALIDATION.UPI_ID.TYPE_ERROR"),
    }),
  }),
};
