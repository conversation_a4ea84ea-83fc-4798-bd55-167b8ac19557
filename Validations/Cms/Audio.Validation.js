const { object, string, boolean, number } = require("yup");

module.exports = {
    addAudioValidation: object({
        body: object({
            musicPreference: string()
                .typeError("VALIDATION.MUSIC.TYPE_ERROR")
                .required('VALIDATION.MUSIC.REQUIRED'),
            file: string().optional().nullable(),
        })
    }),
    listAudioValidation: object({
        body: object({
            page: number()
                .required("VALIDATION.PAGE.REQUIRED")
                .typeError("VALIDATION.PAGE.TYPE_ERROR"),
            sort_by: string()
                .trim()
                .required("VALIDATION.SORT_BY.REQUIRED")
                .typeError("VALIDATION.SORT_BY.TYPE_ERROR"),
            sort_order: string()
                .trim()
                .oneOf(["asc", "desc"], `VALIDATION.SORT_ORDER.ONE_OF`)
                .required("VALIDATION.SORT_ORDER.REQUIRED")
                .typeError("VALIDATION.SORT_ORDER.TYPE_ERROR"),
            search: string()
                .nullable()
                .trim()
                .typeError("VALIDATION.SEARCH.TYPE_ERROR"),
        }),
    }),
    deleteAudioValidation: object({
        body: object({
            id: string()
                .trim()
                .required("VALIDATION.ID.REQUIRED")
                .typeError("VALIDATION.ID.TYPE_ERROR"),
        }),
    }),
    audioDetailsValidation: object({
        body: object({
            id: string()
                .trim()
                .required("VALIDATION.ID.REQUIRED")
                .typeError("VALIDATION.ID.TYPE_ERROR"),
        }),
    }),
    editAudioValidation: object({
        body: object({
            id: string()
                .trim()
                .required("VALIDATION.ID.REQUIRED")
                .typeError("VALIDATION.ID.TYPE_ERROR"),
            musicPreference: string()
                .typeError("VALIDATION.MUSIC.TYPE_ERROR")
                .required('VALIDATION.MUSIC.REQUIRED'),
            file: string().optional().nullable(),
        })
    }),
}