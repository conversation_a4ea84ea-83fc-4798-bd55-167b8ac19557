const { object, string, number } = require("yup");

module.exports = {
  addStaticPageValidation: object({
    body: object({
      type: string()
        .trim()
        .required("VALIDATION.PAGE_TYPE.REQUIRED")
        .typeError("VALIDATION.PAGE_TYPE.TYPE_ERROR"),
      title: string()
        .trim()
        .required("VALIDATION.PAGE_TITLE.REQUIRED")
        .typeError("VALIDATION.PAGE_TITLE.TYPE_ERROR"),
      content: string()
        .trim()
        .required("VALIDATION.PAGE_CONTENT.REQUIRED")
        .typeError("VALIDATION.PAGE_CONTENT.TYPE_ERROR"),
    }),
  }),
  staticPageListValidation: object({
    body: object({
      page: number()
        .required("VALIDATION.PAGE.REQUIRED")
        .typeError("VALIDATION.PAGE.TYPE_ERROR"),
      sort_by: string()
        .trim()
        .required("VALIDATION.SORT_BY.REQUIRED")
        .typeError("VALIDATION.SORT_BY.TYPE_ERROR"),
      sort_order: string()
        .trim()
        .oneOf(["asc", "desc"], `VALIDATION.SORT_ORDER.ONE_OF`)
        .required("VALIDATION.SORT_ORDER.REQUIRED")
        .typeError("VALIDATION.SORT_ORDER.TYPE_ERROR"),
      search: string()
        .nullable()
        .trim()
        .typeError("VALIDATION.SEARCH.TYPE_ERROR"),
    }),
  }),
  staticPageDetailsValidation: object({
    body: object({
      id: string()
        .trim()
        .required("VALIDATION.ID.REQUIRED")
        .typeError("VALIDATION.ID.TYPE_ERROR"),
    }),
  }),
  editPageValidation: object({
    body: object({
      id: string()
        .trim()
        .required("VALIDATION.ID.REQUIRED")
        .typeError("VALIDATION.ID.TYPE_ERROR"),
      content: string()
        .trim()
        .required("VALIDATION.PAGE_CONTENT.REQUIRED")
        .typeError("VALIDATION.PAGE_CONTENT.TYPE_ERROR"),
      title: string()
        .trim()
        .required("VALIDATION.PAGE_TITLE.REQUIRED")
        .typeError("VALIDATION.PAGE_TITLE.TYPE_ERROR"),
      type: string()
        .trim()
        .required("VALIDATION.PAGE_TYPE.REQUIRED")
        .typeError("VALIDATION.PAGE_TYPE.TYPE_ERROR"),
    }),
  }),
  staticPageDeleteValidation: object({
    body: object({
      id: string()
        .trim()
        .required("VALIDATION.ID.REQUIRED")
        .typeError("VALIDATION.ID.TYPE_ERROR"),
    }),
  }),
};
