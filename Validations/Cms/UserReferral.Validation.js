const { object, string, number, date } = require("yup");

module.exports = {
  addUserReferralValidation: object({
    body: object({
      user_id: string()
        .trim()
        .required("VALIDATION.USER_ID.REQUIRED")
        .typeError("VALIDATION.USER_ID.TYPE_ERROR"),
      amount: number()
        .required("VALIDATION.AMOUNT.REQUIRED")
        .typeError("VALIDATION.AMOUNT.TYPE_ERROR"),
      date: string()
        .trim()
        .required("VALIDATION.DATE.REQUIRED")
        .matches(/^\d{4}-\d{1,2}-\d{1,2}$/, "VALIDATION.DATE.FORMAT_ERROR")
        .typeError("VALIDATION.DATE.TYPE_ERROR"),
    }),
  }),

  listUserReferralValidation: object({
    body: object({
      page: number()
        .required("VALIDATION.PAGE.REQUIRED")
        .typeError("VALIDATION.PAGE.TYPE_ERROR"),
      sort_by: string()
        .trim()
        .required("VALIDATION.SORT_BY.REQUIRED")
        .typeError("VALIDATION.SORT_BY.TYPE_ERROR"),
      sort_order: string()
        .trim()
        .oneOf(["asc", "desc"], `VALIDATION.SORT_ORDER.ONE_OF`)
        .required("VALIDATION.SORT_ORDER.REQUIRED")
        .typeError("VALIDATION.SORT_ORDER.TYPE_ERROR"),
      search: string()
        .nullable()
        .trim()
        .typeError("VALIDATION.SEARCH.TYPE_ERROR"),
      month: number()
        .nullable()
        .min(1, "VALIDATION.MONTH.MIN")
        .max(12, "VALIDATION.MONTH.MAX")
        .typeError("VALIDATION.MONTH.TYPE_ERROR"),
      year: number()
        .nullable()
        .min(2000, "VALIDATION.YEAR.MIN")
        .max(2100, "VALIDATION.YEAR.MAX")
        .typeError("VALIDATION.YEAR.TYPE_ERROR"),
      user_id: string()
        .nullable()
        .trim()
        .typeError("VALIDATION.USER_ID.TYPE_ERROR"),
    }),
  }),
};
