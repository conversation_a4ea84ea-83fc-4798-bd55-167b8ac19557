const { object, string, number } = require("yup");

module.exports = {
    ImageValidation: object({
        body: object({
            page: number()
                .required('VALIDATION.PAGE.REQUIRED')
                .typeError('VALIDATION.PAGE.TYPE_ERROR'),
            sort_by: string()
                .trim()
                .required('VALIDATION.SORT_BY.REQUIRED')
                .typeError('VALIDATION.SORT_BY.TYPE_ERROR'),
            sort_order: string()
                .trim()
                .oneOf(['asc', 'desc'], `VALIDATION.SORT_ORDER.ONE_OF`)
                .required('VALIDATION.SORT_ORDER.REQUIRED')
                .typeError('VALIDATION.SORT_ORDER.TYPE_ERROR'),
        })
    }),
}