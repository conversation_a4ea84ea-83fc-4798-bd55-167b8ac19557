const { object, string, number } = require('yup');

module.exports = {
	loginValidation: object({
		body: object({
			email: string()
				.email('VALIDATION.EMAIL.INVALID')
				.required('VALIDATION.EMAIL.REQUIRED'),
			password: string().required('VALIDATION.PASSWORD.REQUIRED')
		})
	}),

	editProfileValidation: object({
		body: object({
			fullName: string()
				.trim()
				.matches(/^[aA-zZ\s]+$/, 'VALIDATION.NAME.VALID')
				.typeError('VALIDATION.NAME.TYPE_ERROR'),
			profileImage: string().optional().nullable(),
			id: number().required('VALIDATION.ID.REQUIRED')
		}).noUnknown(true)
	}),

	changePasswordValidation: object({
		body: object({
			oldPassword: string().required('VALIDATION.CURRENT_PASSWORD.REQUIRED'),
			newPassword: string().required('VALIDATION.NEW_PASSWORD.REQUIRED')
		}).noUnknown(true)
	}),

	forgotPasswordValidation: object({
		body: object({
			email: string()
				.email('VALIDATION.EMAIL.INVALID')
				.required('VALIDATION.EMAIL.REQUIRED'),
		}).noUnknown(true)
	}),

	verifyTokenValidation:object({
		body: object({
			token: string()
				.required('VALIDATION.TOKEN.REQUIRED'),
		}).noUnknown(true)
	}),

	resetPasswordValidation:object({
		body: object({
			password: string().required('VALIDATION.PASSWORD.REQUIRED')
		}).noUnknown(true)
	})
};
