const { object, string, boolean, number } = require("yup");

module.exports = {
    createCategoryValidation: object({
        body: object({
            name: string()
                .trim()
                // .matches(/^[aA-zZ0-9\s\-',.]+$/, 'VALIDATION.CATEGORY_NAME.VALID') // Including more special characters
                .typeError('VALIDATION.CATEGORY_NAME.TYPE_ERROR'),
            description: string()
                .trim()
                .typeError('VALIDATION.DESCRIPTION.TYPE_ERROR'),
            subTitle: string()
                .nullable()
                .trim()
                .typeError("VALIDATION.SUBTITLE.TYPE_ERROR"),
            parentId: number()
                .nullable()
                .typeError("VALIDATION.PARENTID.TYPE_ERROR"),
            haveSubCategory: string()
                .required('VALIDATION.HAVE_SUB_CATEGORY.REQUIRED')
                .oneOf(["true", "false"], 'VALIDATION.HAVE_SUB_CATEGORY.TYPE_ERROR'),
            bannerImage: string().optional().nullable(),
            guideline: string()
                .nullable()
                .trim()
                .typeError("VALIDATION.GUIDELINE.TYPE_ERROR"),
            precaution: string()
                .nullable()
                .trim()
                .typeError("VALIDATION.PRECAUTION.TYPE_ERROR"),
        })
    }),
    listCategoryValidation: object({
        body: object({
            page: number()
                .required("VALIDATION.PAGE.REQUIRED")
                .typeError("VALIDATION.PAGE.TYPE_ERROR"),
            sort_by: string()
                .trim()
                .required("VALIDATION.SORT_BY.REQUIRED")
                .typeError("VALIDATION.SORT_BY.TYPE_ERROR"),
            sort_order: string()
                .trim()
                .oneOf(["asc", "desc"], `VALIDATION.SORT_ORDER.ONE_OF`)
                .required("VALIDATION.SORT_ORDER.REQUIRED")
                .typeError("VALIDATION.SORT_ORDER.TYPE_ERROR"),
            search: string()
                .nullable()
                .trim()
                .typeError("VALIDATION.SEARCH.TYPE_ERROR"),
        }),
    }),
    categoryDetailsValidation: object({
        body: object({
            id: string()
                .trim()
                .required("VALIDATION.ID.REQUIRED")
                .typeError("VALIDATION.ID.TYPE_ERROR"),
        }),
    }),
    addPlaylistValidation: object({
        body: object({
            id: string()
                .nullable()
                .optional()
                .typeError("VALIDATION.ID.TYPE_ERROR"),
            categoryId: string()
                .required("VALIDATION.CATEGORY_ID.REQUIRED")
                .typeError("VALIDATION.CATEGORY_ID.TYPE_ERROR"),
            title: string()
                .trim()
                .matches(/^[a-zA-Z0-9\s!@#$%^&*()_+=:;'",.<>?/\\-]*$/, 'VALIDATION.PLAYLIST_TITLE.VALID')
                .typeError('VALIDATION.PLAYLIST_TITLE.TYPE_ERROR'),
            benefit: string()
                .trim()
                .nullable()
                .typeError("VALIDATION.BENIFIT.TYPE_ERROR"),
            practice: string()
                .trim()
                .nullable()
                .typeError("VALIDATION.PRACTICE.TYPE_ERROR"),
            duration: string()
                .required("VALIDATION.DURATION.REQUIRED")
                .typeError('VALIDATION.DURATION.TYPE_ERROR'),
        })
    }),
    getPlaylistValidation: object({
        body: object({
            id: string()
                .trim()
                .required("VALIDATION.ID.REQUIRED")
                .typeError("VALIDATION.ID.TYPE_ERROR"),
        }),
    }),
    editCategoryValidation: object({
        body: object({
            id: string()
                .trim()
                .required("VALIDATION.ID.REQUIRED")
                .typeError("VALIDATION.ID.TYPE_ERROR"),
            name: string()
                .trim()
                // .matches(/^[aA-zZ0-9\s\-',.]+$/, 'VALIDATION.CATEGORY_NAME.VALID') // Including more special characters
                .typeError('VALIDATION.CATEGORY_NAME.TYPE_ERROR'),
            description: string()
                .trim()
                .typeError('VALIDATION.DESCRIPTION.TYPE_ERROR'),
            subTitle: string()
                .nullable()
                .trim()
                .typeError("VALIDATION.SUBTITLE.TYPE_ERROR"),
            parentId: number()
                .nullable()
                .typeError("VALIDATION.PARENTID.TYPE_ERROR"),
            haveSubCategory: string()
                .required('VALIDATION.HAVE_SUB_CATEGORY.REQUIRED')
                .oneOf(["true", "false"], 'VALIDATION.HAVE_SUB_CATEGORY.TYPE_ERROR'),
            bannerImage: string().optional().nullable(),
            guideline: string()
                .nullable()
                .trim()
                .typeError("VALIDATION.GUIDELINE.TYPE_ERROR"),
            precaution: string()
                .nullable()
                .trim()
                .typeError("VALIDATION.PRECAUTION.TYPE_ERROR"),
        })
    }),

    categoryDeleteValidation: object({
        body: object({
            id: string()
                .trim()
                .required("VALIDATION.ID.REQUIRED")
                .typeError("VALIDATION.ID.TYPE_ERROR"),
        }),
    }),

    getRecommendationValidation: object({
        body: object({
            id: string()
                .trim()
                .required("VALIDATION.ID.REQUIRED")
                .typeError("VALIDATION.ID.TYPE_ERROR"),
        })
    }),
    listRecommendationValidation: object({
        body: object({
            page: number()
                .required("VALIDATION.PAGE.REQUIRED")
                .typeError("VALIDATION.PAGE.TYPE_ERROR"),
            sort_by: string()
                .trim()
                .required("VALIDATION.SORT_BY.REQUIRED")
                .typeError("VALIDATION.SORT_BY.TYPE_ERROR"),
            sort_order: string()
                .trim()
                .oneOf(["asc", "desc"], `VALIDATION.SORT_ORDER.ONE_OF`)
                .required("VALIDATION.SORT_ORDER.REQUIRED")
                .typeError("VALIDATION.SORT_ORDER.TYPE_ERROR"),
            search: string()
                .nullable()
                .trim()
                .typeError("VALIDATION.SEARCH.TYPE_ERROR"),
        }),
    }),
    deletePlaylistValidation: object({
        body: object({
            id: string()
                .trim()
                .required("VALIDATION.ID.REQUIRED")
                .typeError("VALIDATION.ID.TYPE_ERROR"),
        }),
    }),
}