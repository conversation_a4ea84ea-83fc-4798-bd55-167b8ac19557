const { object, string, number, boolean, mixed, date, ref } = require('yup');

module.exports = {
    listUserValidation: object({
        body: object({
            page: number()
                .required('VALIDATION.PAGE.REQUIRED')
                .typeError('VALIDATION.PAGE.TYPE_ERROR'),
            sort_by: string()
                .trim()
                .required('VALIDATION.SORT_BY.REQUIRED')
                .typeError('VALIDATION.SORT_BY.TYPE_ERROR'),
            sort_order: string()
                .trim()
                .oneOf(['asc', 'desc'], `VALIDATION.SORT_ORDER.ONE_OF`)
                .required('VALIDATION.SORT_ORDER.REQUIRED')
                .typeError('VALIDATION.SORT_ORDER.TYPE_ERROR'),
            search: string()
                .nullable()
                .trim()
                .typeError('VALIDATION.SEARCH.TYPE_ERROR')
        })
    }),

    userDetailsValidation: object({
        body: object({
            id: string()
                .trim()
                .required('VALIDATION.ID.REQUIRED')
                .typeError('VALIDATION.ID.TYPE_ERROR')
        })
    }),

    editUserDetailsValidation: object({
        body: object({
            user_id: string()
                .trim()
                .required('VALIDATION.USERID.REQUIRED')
                .typeError('VALIDATION.USERID.TYPE_ERROR'),
        })
    }),

    updateStatusValidation: object({
        body: object({
            id: string()
                .trim()
                .required('VALIDATION.ID.REQUIRED')
                .typeError('VALIDATION.ID.TYPE_ERROR'),
            status: string()
                .trim()
                .required('VALIDATION.STATUS.REQUIRED')
                .typeError('VALIDATION.STATUS.TYPE_ERROR'),
        })
    }),

    addUserValidation: object({
        body: object({
            firstName: string()
                .trim()
                .required('VALIDATION.FULLNAME.REQUIRED')
                .typeError('VALIDATION.FULLNAME.TYPE_ERROR'),
            lastName: string()
                .trim()
                .required('VALIDATION.FULLNAME.REQUIRED')
                .typeError('VALIDATION.FULLNAME.TYPE_ERROR'),
            email: string()
                .email('VALIDATION.EMAIL.INVALID')
                .required('VALIDATION.EMAIL.REQUIRED'),
            password: string()
                .required('VALIDATION.PASSWORD.REQUIRED'),
            avatar: mixed()
                .nullable()
                .notRequired(),
        })
    }),

    progressDataValidation: object({
        body: object({
            userId: string()
                .trim()
                .required('VALIDATION.ID.REQUIRED')
                .typeError('VALIDATION.ID.TYPE_ERROR'),
            endDate: date()
                .required('VALIDATION.END_DATE.REQUIRED')
                .typeError('VALIDATION.END_DATE.TYPE_ERROR')
        })
    }),

    deleteUserValidation: object({
        query: object({
            id: string()
                .trim()
                .required('VALIDATION.ID.REQUIRED')
                .typeError('VALIDATION.ID.TYPE_ERROR'),
        })
    }),
}