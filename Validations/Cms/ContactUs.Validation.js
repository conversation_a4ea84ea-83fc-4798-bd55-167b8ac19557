const { object, string, number } = require('yup');

module.exports = {
    ContactUsListValidation: object({
        body: object({
            page: number()
                .required('VALIDATION.PAGE.REQUIRED')
                .typeError('VALIDATION.PAGE.TYPE_ERROR'),
            sort_by: string()
                .trim()
                .required('VALIDATION.SORT_BY.REQUIRED')
                .typeError('VALIDATION.SORT_BY.TYPE_ERROR'),
            sort_order: string()
                .trim()
                .oneOf(['asc', 'desc'], `VALIDATION.SORT_ORDER.ONE_OF`)
                .required('VALIDATION.SORT_ORDER.REQUIRED')
                .typeError('VALIDATION.SORT_ORDER.TYPE_ERROR'),
            search: string()
                .nullable()
                .trim()
                .typeError('VALIDATION.SEARCH.TYPE_ERROR')
        })
    }),

    contactUsDetailsValidation: object({
        body: object({
            id: string()
                .trim()
                .required('VALIDATION.ID.REQUIRED')
                .typeError('VALIDATION.ID.TYPE_ERROR')
        })
    }),

    replyContactUsValidation: object({
        body: object({
            email: string()
                .email('VALIDATION.EMAIL.INVALID')  // Checks if the email format is valid
                .required('VALIDATION.EMAIL.REQUIRED')
                .typeError('VALIDATION.EMAIL.TYPE_ERROR'),
            subject: string()
                .trim()
                .min(5, 'VALIDATION.SUBJECT.MIN')  // Minimum 5 characters for subject
                .max(100, 'VALIDATION.SUBJECT.MAX') // Maximum 100 characters for subject
                .required('VALIDATION.SUBJECT.REQUIRED')
                .typeError('VALIDATION.SUBJECT.TYPE_ERROR'),
            body: string()
                .trim()
                .min(10, 'VALIDATION.BODY.MIN')  // Minimum 10 characters for body
                .max(1000, 'VALIDATION.BODY.MAX') // Maximum 1000 characters for body
                .required('VALIDATION.BODY.REQUIRED')
                .typeError('VALIDATION.BODY.TYPE_ERROR')
        })
    }),

    deleteContactUsValidation: object({
        query: object({
            id: string()
                .trim()
                .required('VALIDATION.ID.REQUIRED')
                .typeError('VALIDATION.ID.TYPE_ERROR'),
        })
    }),
};
