const JWTTOKEN = {
  algorithm: "HS256",
  secret: "breathworkSecret",
  expiresIn: "2h",
};

const PAGE_LIMIT = 10;

const MAIL_THEME = {
  appName: "Breathwork.ai",
  appLogoUrl: process.env.LOGO || "",
  primaryColor: "#F28500",
  footerColor: "rgba(1,1,1,0.1)",
  footerTextColor: "#000000",
};

const S3_BUCKET_NAME = process.env.S3_BUCKET_NAME;
const S3_URL = process.env.S3_AWS_URL;
const S3_REGION = process.env.S3_REGION;
const S3_ACCESS_KEY = process.env.S3_ACCESSKEY;
const S3_SECRET_KEY = process.env.S3_SECRETKEY;
const S3_PROJECT_FOLDER = process.env.S3_PROJECT_FOLDER;
const S3_USER_FOLDER = process.env.S3_USER_FOLDER;
const S3_CATEGORY_FOLDER = process.env.S3_CATEGORY_FOLDER;
const S3_VIDEO_FOLDER = process.env.S3_VIDEO_FOLDER;
const S3_IMAGE_FOLDER = process.env.S3_IMAGE_FOLDER;
const S3_AUDIO_FOLDER = process.env.S3_AUDIO_FOLDER;
const S3_REFERRAL_FOLDER = process.env.S3_REFERRAL_FOLDER;

const FILE_SIZE = 1024 * 1024 * 5;
const VIDEO_FILE_SIZE = 1024 * 1024 * 200;

const CATEGORY = {
  meditation: "Meditation",
};

const MAIL_SERVICE = "gmail";
const MAIL_HOST = "smtp.gmail.com";
const MAIL_PORT = 587;
const MAIL_SECURE = true;
const MAIL_FROM_NAME = "BreathWork.ai";
const MAIL_FROM_EMAIL = process.env.EMAIL_ID;
const MAIL_FROM_PASS = process.env.EMAIL_PASSWORD;
const ISSUER = process.env.ISSUER;
const APPLE_SECRET = process.env.APPLE_SECRET;
const BUNDEL_ID = process.env.BUNDEL_ID;
const KEY_ID = process.env.KEY_ID;
const NODE_ENV = process.env.NODE_ENV;

const CMS_URL = process.env.CMS_URL;

const SUBSCRIPTION_STATUS = {
  NOT_SUBSCRIBED: "not_subscribed",
  SUBSCRIBED: "subscribed",
  CANCELLED: "cancelled",
};

module.exports = {
  JWTTOKEN,
  PAGE_LIMIT,
  MAIL_THEME,
  FILE_SIZE,
  CATEGORY,
  S3_BUCKET_NAME,
  S3_URL,
  S3_REGION,
  S3_ACCESS_KEY,
  S3_SECRET_KEY,
  S3_PROJECT_FOLDER,
  S3_USER_FOLDER,
  S3_CATEGORY_FOLDER,
  S3_VIDEO_FOLDER,
  VIDEO_FILE_SIZE,
  S3_IMAGE_FOLDER,
  MAIL_SERVICE,
  MAIL_HOST,
  MAIL_PORT,
  MAIL_SECURE,
  MAIL_FROM_NAME,
  MAIL_FROM_EMAIL,
  MAIL_FROM_PASS,
  S3_AUDIO_FOLDER,
  CMS_URL,
  S3_REFERRAL_FOLDER,
  SUBSCRIPTION_STATUS,
  ISSUER,
  APPLE_SECRET,
  BUNDEL_ID,
  KEY_ID,
  NODE_ENV,
};
