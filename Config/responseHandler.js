/* 
HOW TO USE THIS TO SEND RESPONSE

In your controller you can use

res.handler.*function*(data object*, message* , error*)
Ex :
res.handler.custom(200, "User created", {userName : "<PERSON>"}, false)
res.handler.success()
res.handler.success("User created", {userName : "<PERSON>"})
res.handler.success("User created", null)
res.handler.success(null, {userName : "<PERSON>"})

for message you can pass simple string
1. We have sent an email to your account
or for with values like
We have sent an email to %s,
{
	key : "TRANSLATION KEY",
	value : "value of %s"
}
*/

class ResponseHandler {
	constructor(req, res) {
		this.req = req;
		this.res = res;
	}

	sender(code, message, data, status) {
		this.res.status(code).json({
			status: status,
			message:
				typeof message === 'string'
					? this.res.__(message)
					: this.res.__(message.key, message.value),
			data: data ? data : null
		});
	}

	/* 
		ARGUMENTS : Status code, message, data object,  status = 0/1
	*/
	custom(code, message, data, status) {
		this.sender(code, message, data, status);
	}

	/* 
		ARGUMENTS : data o̥̥bject, message, error object
	*/

	// 2XX SUCCESS
	success(message, data = {}, status = 1) {
		this.sender(200, message || 'STATUS.SUCCESS', data, status);
	}

	created(message, data = {}, status = 1) {
		this.sender(201, message || 'STATUS.CREATED', data, status);
	}

	// 4XX CLIENT ERROR
	badRequest(message, data = {}, status = 0) {
		this.sender(400, message || 'STATUS.BAD_REQUEST', data, status);
	}

	unauthorized(message, data = {}, status = 0) {
		this.sender(401, message || 'STATUS.UNAUTHORIZED', data, status);
	}

	forbidden(message, data = {}, status = 0) {
		this.sender(403, message || 'STATUS.FORBIDDEN', data, status);
	}

	notFound(message, data = {}, status = 0) {
		this.sender(404, message || 'STATUS.NOT_FOUND', data, status);
	}

	notAllowed(message, data = {}, status = 0) {
		this.sender(405, message || 'STATUS.NOT_ALLOWED', data, status);
	}

	requestTimeout(message, data = {}, status = 0) {
		this.sender(408, message || 'STATUS.REQUEST_TIMEOUT', data, status);
	}

	conflict(message, data = {}, status = 0) {
		this.sender(409, message || 'STATUS.CONFLICT', data, status);
	}

	preconditionFailed(message, data = {}, status = 0) {
		this.sender(412, message || 'STATUS.PRECONDITION_FAILED', data, status);
	}

	validationError(message, data = {}, status = 0) {
		this.sender(422, message || 'STATUS.VALIDATION_ERROR', data, status);
	}

	tooManyRequests(message, data = {}, status = 0) {
		this.sender(429, message || 'STATUS.TOO_MANY_REQUESTS', data, status);
	}

	// 5XX SERVER ERROR
	internalError(message, data = {}, status = 0) {
		this.sender(500, message || 'STATUS.INTERNAL_ERROR', data, status);
	}

	badGateway(message, data = {}, status = 0) {
		this.sender(502, message || 'STATUS.BAD_GATEWAY', data, status);
	}

	unavailable(message, data = {}, status = 0) {
		this.sender(503, message || 'STATUS.UNAVAILABLE', data, status);
	}

	gatewayTimeout(message, data = {}, status = 0) {
		this.sender(504, message || 'STATUS.GATEWAY_TIMEOUT', data, status);
	}
}

module.exports = ResponseHandler;
