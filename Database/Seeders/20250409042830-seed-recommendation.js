'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('recommendation', [
      { name: 'Reduce anxiety', createdAt: new Date(), updatedAt: new Date() },
      { name: 'Reduce stress', createdAt: new Date(), updatedAt: new Date() },
      { name: 'Better Sleep', createdAt: new Date(), updatedAt: new Date() },
      { name: 'Improve focus & concentration', createdAt: new Date(), updatedAt: new Date() },
      { name: 'Better health & healing', createdAt: new Date(), updatedAt: new Date() },
      { name: 'Increase happiness', createdAt: new Date(), updatedAt: new Date() },
      { name: 'Improve athletic performance', createdAt: new Date(), updatedAt: new Date() },
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('recommendation', null, {});
  }
};
