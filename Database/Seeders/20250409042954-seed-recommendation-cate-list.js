'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('recommendationCateList', [
      // Reduce anxiety
      { name: 'Breathwork- Reduce Anxiety', recommendationId: 1, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Pranayama- inner peace', recommendationId: 1, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Meditation- relaxation and well-being', recommendationId: 1, createdAt: new Date(), updatedAt: new Date() },

      // Reduce stress
      { name: 'Breathwork- reduce stress', recommendationId: 2, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Pranayama- calm your mind', recommendationId: 2, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Meditation- relaxation and well-being', recommendationId: 2, createdAt: new Date(), updatedAt: new Date() },

      // Better Sleep
      { name: 'Breathwork- deepening sleep', recommendationId: 3, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Pranayama- inner peace', recommendationId: 3, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Meditation- sleep meditation', recommendationId: 3, createdAt: new Date(), updatedAt: new Date() },

      // Improve focus & concentration
      { name: 'Breathwork- concentration', recommendationId: 4, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Pranayama- deep relaxation', recommendationId: 4, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Meditation- improve performance & focus', recommendationId: 4, createdAt: new Date(), updatedAt: new Date() },

      // Better health & healing
      { name: 'Breathwork- health & healing', recommendationId: 5, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Pranayama- awaken your senses', recommendationId: 5, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Meditation- health & healing', recommendationId: 5, createdAt: new Date(), updatedAt: new Date() },

      // Increase happiness
      { name: 'Breathwork- good mood', recommendationId: 6, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Pranayama- floating serenity', recommendationId: 6, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Meditation- mood booster', recommendationId: 6, createdAt: new Date(), updatedAt: new Date() },

      // Improve athletic performance
      { name: 'Breathwork- athletic breathwork', recommendationId: 7, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Pranayama- revitalise your body', recommendationId: 7, createdAt: new Date(), updatedAt: new Date() },
      { name: 'Meditation- improve performance & focus', recommendationId: 7, createdAt: new Date(), updatedAt: new Date() },
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('recommendationCateList', null, {});
  }
};
