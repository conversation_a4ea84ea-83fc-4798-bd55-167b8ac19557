const bcrypt = require('bcrypt');

module.exports = {
	up: async (queryInterface, Sequelize) => {
		return queryInterface.bulkInsert('admins', [
			{
				email: '<EMAIL>',
				fullName: 'Super Admin',
				password: await bcrypt.hash('Admin@123', 10),
				createdAt: new Date(),
				isActive: true
			}
		]);
	},
	down: (queryInterface, Sequelize) => {
		return queryInterface.bulkDelete('admins', null, {});
	}
};
