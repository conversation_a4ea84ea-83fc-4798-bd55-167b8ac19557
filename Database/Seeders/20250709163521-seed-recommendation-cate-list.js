"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Define name updates in a more maintainable structure
    const nameUpdates = [
      {
        condition: {
          name: "Breathwork- Athletic Breathwork",
          recommendationId: 7,
        },
        data: { type: "Tab" },
      },
      {
        condition: {
          name: "<PERSON><PERSON><PERSON>- revitalise your body",
          recommendationId: 7,
        },
        data: { name: "Pranayama - Puraka - Kumbhaka - Rechaka" },
      },
      {
        condition: { name: "Breathwork- Good Mood", recommendationId: 6 },
        data: { type: "Exercise" },
      },
      {
        condition: {
          name: "Pranayama- floating serenity",
          recommendationId: 6,
        },
        data: { name: "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>" },
      },
      {
        condition: { name: "Meditation- mood booster", recommendationId: 6 },
        data: { name: "Meditation - Self-Care" },
      },
      {
        condition: {
          name: "Breathwork- Health & Healing",
          recommendationId: 5,
        },
        data: { type: "SubCategory" },
      },
      {
        condition: {
          name: "<PERSON><PERSON><PERSON>- awaken your senses",
          recommendationId: 5,
        },
        data: { name: "<PERSON><PERSON><PERSON> - <PERSON><PERSON>" },
      },
      {
        condition: { name: "Breathwork- Concentration", recommendationId: 4 },
        data: { name: "Breathwork - Box breathing", type: "Exercise" },
      },
      {
        condition: { name: "Pranayama- Deep Relaxation", recommendationId: 4 },
        data: { name: "Pranayama - Anuloma Krama" },
      },
      {
        condition: { name: "Breathwork- Deepening Sleep", recommendationId: 3 },
        data: { type: "Exercise" },
      },
      {
        condition: { name: "Pranayama- Inner Peace", recommendationId: 3 },
        data: { name: "Pranayama - Chandrabhedi" },
      },
      {
        condition: {
          name: "Meditation- Sleep Meditation",
          recommendationId: 3,
        },
        data: { name: "Meditation - Relaxation and Well-being" },
      },
      {
        condition: { name: "Breathwork- Reduce Stress", recommendationId: 2 },
        data: { type: "Exercise" },
      },
      {
        condition: { name: "Pranayama- Calm Your Mind", recommendationId: 2 },
        data: { name: "Pranayama - Bhramari" },
      },
      {
        condition: { name: "Breathwork- Reduce Anxiety", recommendationId: 1 },
        data: { type: "Exercise" },
      },
      {
        condition: { name: "Pranayama- Inner Peace", recommendationId: 1 },
        data: { name: "Pranayama - Anulom Vilom" },
      },
    ];

    // Execute all updates using Promise.all for better performance
    await Promise.all(
      nameUpdates.map(({ data, condition }) =>
        queryInterface.bulkUpdate("recommendationCateList", data, condition)
      )
    );
  },

  down: async (queryInterface, Sequelize) => {
    // Define rollback updates (reverse the changes)
  },
};
