"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert("app_info", [
      {
        iOSVersion: "1.0.0",
        androidVersion: "1.0.0",
        isForceUpdate: false,
        isSoftUpdate: false,
        updateMessage: "A new version is available with exciting features and improvements!",
        androidStoreUrl: "https://play.google.com/store/apps/details?id=com.whatsapp",
        iOSStoreUrl: "https://apps.apple.com/app/id310633997",
        isActive: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete("app_info", null, {});
  },
};
