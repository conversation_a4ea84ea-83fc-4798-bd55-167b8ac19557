module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.bulkInsert("staticPages", [
      {
        type: "contacu_us",
        title: "Contact Us",
        content: "<ol><li>These are the Contact us content.</li></ol>",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        type: "privacy_policy",
        title: "Privacy Policy",
        content: "<ol><li>These are the Privacy Policy page content.</li></ol>",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        type: "terms_and_conditions",
        title: "Terms and Conditions",
        content: "<ol><li>These are the Terms and Conditions page content.</li></ol>",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete("staticPages", null, {});
  },
};
