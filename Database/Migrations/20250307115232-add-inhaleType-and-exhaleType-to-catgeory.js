'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('categories', 'inhaleType', {
      type: Sequelize.STRING,
      allowNull: true, // Change to false if it's mandatory
    });

    await queryInterface.addColumn('categories', 'exhaleType', {
      type: Sequelize.STRING,
      allowNull: true, // Change to false if it's mandatory
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('categories', 'inhaleType');
    await queryInterface.removeColumn('categories', 'exhaleType');
  }
};
