"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("users", "purchaseDate", {
      type: Sequelize.DATE,
      allowNull: true,
      comment: "Date when subscription was purchased",
    });

    await queryInterface.addColumn("users", "expiryDate", {
      type: Sequelize.DATE,
      allowNull: true,
      comment: "Date when subscription expires",
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn("users", "purchaseDate");
    await queryInterface.removeColumn("users", "expiryDate");
  },
};
