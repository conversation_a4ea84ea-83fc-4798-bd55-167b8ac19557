"use strict";

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable("app_info", {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      iOSVersion: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: "Current iOS app version"
      },
      androidVersion: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: "Current Android app version"
      },
      isForceUpdate: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Whether force update is required"
      },
      isSoftUpdate: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Whether soft update is available"
      },
      updateMessage: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "Message to show for app update"
      },
      androidStoreUrl: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: "Google Play Store URL"
      },
      iOSStoreUrl: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: "Apple App Store URL"
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: "Whether this app info record is active"
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: null,
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable("app_info");
  },
};
