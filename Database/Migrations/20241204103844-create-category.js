'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('categories', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      haveSubCategory: {
        type: Sequelize.BOOLEAN,
      },
      name: {
        allowNull: false,
        type: Sequelize.STRING
      },
      subTitle: {
        type: Sequelize.STRING
      },
      parentId: Sequelize.INTEGER,
      description: {
        type: Sequelize.TEXT
      },
      bannerImage: {
        type: Sequelize.TEXT,
      },
      guideline: {
        type: Sequelize.TEXT
      },
      precaution: {
        type: Sequelize.TEXT,
      },
      holdTime: {
        type: Sequelize.INTEGER,
      },
      inhaleTime: {
        type: Sequelize.INTEGER,
      },
      exhaleTime: {
        type: Sequelize.INTEGER,
      },
      haveExercise: {
        type: Sequelize.BOOLEAN,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: null
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
    })
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('categories');
  }
};
