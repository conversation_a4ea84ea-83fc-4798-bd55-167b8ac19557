"use strict";

const { SUBSCRIPTION_STATUS } = require("../../Config/constants");

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("users", "subscriptionStatus", {
      type: Sequelize.ENUM(
        SUBSCRIPTION_STATUS.NOT_SUBSCRIBED,
        SUBSCRIPTION_STATUS.SUBSCRIBED,
        SUBSCRIPTION_STATUS.CANCELLED
      ),
      defaultValue: SUBSCRIPTION_STATUS.NOT_SUBSCRIBED,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn("users", "subscriptionStatus");
  },
};
