'use strict';
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('staticPages', 'content', {
      type: Sequelize.TEXT,
      allowNull: true, // If content is nullable
    });
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('staticPages', 'content', {
      type: Sequelize.STRING,
      allowNull: true, // Reverting back to STRING if it was nullable
    });
  }
};
