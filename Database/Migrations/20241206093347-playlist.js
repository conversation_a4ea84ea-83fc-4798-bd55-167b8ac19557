'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('playlists', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      title: {
        allowNull: false,
        type: Sequelize.STRING
      },
      categoryId: Sequelize.INTEGER,
      description: {
        type: Sequelize.TEXT
      },
      file: {
        type: Sequelize.TEXT
      },
      banner: {
        type: Sequelize.TEXT
      },
      benefit: {
        type: Sequelize.TEXT
      },
      practice: {
        type: Sequelize.TEXT
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: null
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
    })
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('playlists');
  }
};
