'use strict';

module.exports = {
    up: async (queryInterface, Sequelize) => {
        await queryInterface.addColumn('categories', 'recommendation', {
            type: Sequelize.STRING,
            allowNull: true,
        });

        await queryInterface.addColumn('categories', 'isAimCat', {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false,
        });
    },

    down: async (queryInterface, Sequelize) => {
        await queryInterface.removeColumn('categories', 'recommendation');
        await queryInterface.removeColumn('categories', 'isAimCat');
    }
};
