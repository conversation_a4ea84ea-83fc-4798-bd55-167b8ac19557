"use strict";

const { S3_URL, S3_IMAGE_FOLDER, S3_PROJECT_FOLDER } = require("../../Config/constants");

module.exports = (sequelize, DataTypes) => {
    const appContent = sequelize.define(
        "appContent",
        {
            title: DataTypes.STRING,
            bannerImage: {
                type: DataTypes.TEXT,
                get() {
                    const rawValue = this.getDataValue('bannerImage');
                    return rawValue ? S3_URL + S3_PROJECT_FOLDER + S3_IMAGE_FOLDER + '/' + rawValue : null;
                }
            },
            isActive: {
                type: DataTypes.BOOLEAN,
                default: false,
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
            },
            updatedAt: {
                type: DataTypes.DATE,
            },
            deletedAt: {
                type: DataTypes.DATE,
                allowNull: true,
            },
        },
        {
            timestamps: true,
            paranoid: true,
        }
    );
    appContent.associate = function (models) {
        // associations can be defined here
    };
    return appContent;
};
