"use strict";

module.exports = (sequelize, DataTypes) => {
  const userReferralHistory = sequelize.define(
    "userReferralHistory",
    {
      referrerUserId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "User ID who referred (owner of the referral code)",
      },
      referredUserId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "User ID who was referred (new user who used the referral code)",
      },
      referralCodeUsed: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "The referral code that was used during signup",
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        defaultValue: null,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      tableName: "user_referral_history",
      timestamps: true,
      paranoid: true,
    }
  );

  userReferralHistory.associate = function (models) {
    // Association with the user who referred (referrer)
    userReferralHistory.belongsTo(models.user, {
      foreignKey: "referrerUserId",
      as: "referrer",
    });

    // Association with the user who was referred (referred)
    userReferralHistory.belongsTo(models.user, {
      foreignKey: "referredUserId",
      as: "referred",
    });
  };

  return userReferralHistory;
};
