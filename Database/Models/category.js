"use strict";

const { S3_CATEGORY_FOLDER, S3_URL, S3_PROJECT_FOLDER } = require("../../Config/constants");

module.exports = (sequelize, DataTypes) => {
    const category = sequelize.define(
        "category",
        {
            name: DataTypes.STRING,
            subTitle: DataTypes.STRING,
            description: DataTypes.TEXT,
            parentId: DataTypes.INTEGER,
            haveSubCategory: {
                type: DataTypes.BOOLEAN,
                default: false,
            },
            bannerImage: {
                type: DataTypes.TEXT,
                get() {
                    const rawValue = this.getDataValue('bannerImage');
                    return rawValue ? S3_URL + S3_PROJECT_FOLDER + '/' + S3_CATEGORY_FOLDER + '/' + rawValue : null;
                }
            },
            guideline: DataTypes.TEXT,
            precaution: DataTypes.TEXT,
            holdTime: DataTypes.INTEGER,
            inhaleTime: DataTypes.INTEGER,
            exhaleTime: DataTypes.INTEGER,
            haveExercise: {
                type: DataTypes.BOOLEAN,
                default: false,
            },
            exerciseDurationTime: DataTypes.STRING,
            recommendation: DataTypes.STRING,
            isAimCat: {
                type: DataTypes.BOOLEAN,
                defaultValue: false,
            },
            playlistRecommendation: DataTypes.STRING,
            inhaleType: DataTypes.STRING,
            exhaleType: DataTypes.STRING,
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
            },
            updatedAt: {
                type: DataTypes.DATE,
            },
            deletedAt: {
                type: DataTypes.DATE,
                allowNull: true,
            },
        },
        {
            timestamps: true,
            paranoid: true,
        }
    );
    category.associate = function (models) {
        // associations can be defined here
        category.hasMany(models.playlist, { foreignKey: 'categoryId' });
        category.belongsTo(models.category, { as: 'parent', foreignKey: 'parentId' });
    };
    return category;
};
