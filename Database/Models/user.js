"use strict";
const bcrypt = require("bcrypt");
const {
  S3_URL,
  S3_USER_FOLDER,
  SUBSCRIPTION_STATUS,
} = require("../../Config/constants");

module.exports = (sequelize, DataTypes) => {
  const user = sequelize.define(
    "user",
    {
      firstName: DataTypes.STRING,
      lastName: DataTypes.STRING,
      email: {
        type: DataTypes.STRING,
        unique: {
          args: true,
          msg: "Email address already registered",
          fields: [sequelize.fn("lower", sequelize.col("email"))],
        },
      },
      isTerms: {
        type: DataTypes.BOOLEAN,
        default: false,
      },
      password: DataTypes.STRING,
      audioPreference: DataTypes.STRING,
      musicPreference: DataTypes.STRING,
      resetPasswordOtp: {
        type: DataTypes.STRING,
        defaultValue: null,
      },
      resetPasswordOtpExpire: {
        type: DataTypes.DATE,
        defaultValue: null,
      },
      status: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      referralCode: {
        type: DataTypes.STRING,
        allowNull: true,
        unique: true,
        comment: "Unique referral code for the user",
      },
      upiId: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "UPI ID for payment transactions",
      },
      purchaseDate: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "Date when subscription was purchased",
      },
      expiryDate: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "Date when subscription expires",
      },
      aim: DataTypes.STRING,
      purchaseToken: DataTypes.STRING,
      createdAt: DataTypes.DATE,
      subscriptionStatus: {
        type: DataTypes.ENUM(
          SUBSCRIPTION_STATUS.NOT_SUBSCRIBED,
          SUBSCRIPTION_STATUS.SUBSCRIBED,
          SUBSCRIPTION_STATUS.CANCELLED
        ),
        defaultValue: SUBSCRIPTION_STATUS.NOT_SUBSCRIBED,
      },
      updatedAt: {
        type: DataTypes.DATE,
        defaultValue: null,
      },
      deletedAt: {
        type: DataTypes.DATE,
        defaultValue: null,
      },
    },
    {
      hooks: {
        beforeCreate: async function (user) {
          if (user.password) {
            try {
              const hashedPassword = await bcrypt.hash(user.password, 10);
              user.password = hashedPassword;
            } catch (error) {
              throw new Error(error);
            }
          }
        },
        beforeBulkUpdate: async function (user) {
          if (user.attributes.password) {
            try {
              const hashedPassword = await bcrypt.hash(
                user.attributes.password,
                10
              );
              user.attributes.password = hashedPassword;
            } catch (error) {
              throw new Error(error);
            }
          }
        },
      },
    }
  );
  user.associate = function (models) {
    user.hasMany(models.breathWorkProgress, {
      foreignKey: "userId",
    });
    user.belongsTo(models.recommendation, {
      foreignKey: "aim",
      as: "recommendation",
    });

    // Referral history associations
    user.hasMany(models.userReferralHistory, {
      foreignKey: "referrerUserId",
      as: "referrals", // Users I have referred
    });
    user.hasMany(models.userReferralHistory, {
      foreignKey: "referredUserId",
      as: "referredBy", // How I was referred
    });
  };
  user.prototype.validatePassword = function (password) {
    try {
      return bcrypt.compareSync(password + "", this.password);
    } catch (err) {
      throw new Error("Password validation failed");
    }
  };
  return user;
};
