"use strict";

module.exports = (sequelize, DataTypes) => {
  const appInfo = sequelize.define(
    "appInfo",
    {
      iOSVersion: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "Current iOS app version",
      },
      androidVersion: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "Current Android app version",
      },
      isForceUpdate: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Whether force update is required",
      },
      isSoftUpdate: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Whether soft update is available",
      },
      updateMessage: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "Message to show for app update",
      },
      androidStoreUrl: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Google Play Store URL",
      },
      iOSStoreUrl: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Apple App Store URL",
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: "Whether this app info record is active",
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        defaultValue: null,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      tableName: "app_info",
      timestamps: true,
      paranoid: true,
    }
  );

  appInfo.associate = function (models) {
    // Define associations here if needed
  };

  return appInfo;
};
