"use strict";
module.exports = (sequelize, DataTypes) => {
  const staticPage = sequelize.define(
    "staticPage",
    {
      type: DataTypes.STRING,
      title: DataTypes.STRING,
      content: DataTypes.TEXT,
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
      },
    },
    {
      timestamps: true,
    }
  );
  staticPage.associate = function (models) {
    // associations can be defined here
  };
  return staticPage;
};
