"use strict";
module.exports = (sequelize, DataTypes) => {
  const userToken = sequelize.define(
    "userToken",
    {
      accessToken: DataTypes.STRING,
      userId: DataTypes.INTEGER,
      deviceToken: DataTypes.STRING,
      deviceType: DataTypes.ENUM("ios", "android"),
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        defaultValue: null,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      paranoid: true,
    }
  );
  userToken.associate = function (models) {
    // associations can be defined here
  };
  return userToken;
};
