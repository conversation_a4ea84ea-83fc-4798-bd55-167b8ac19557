'use strict';

module.exports = (sequelize, DataTypes) => {

    const recommendation = sequelize.define(
        "recommendation", {
        name: {
            type: DataTypes.STRING,
            allowNull: false
        }
    }, {
        sequelize,
        modelName: 'recommendation',
        tableName: 'recommendation',
        timestamps: true,
        paranoid: true // enables soft deletes (deletedAt)
    });
    recommendation.associate = function (models) {
        recommendation.hasMany(models.recommendationCateList, {
            foreignKey: 'recommendationId',
            as: 'categories'
        });
    };

    return recommendation;
};
