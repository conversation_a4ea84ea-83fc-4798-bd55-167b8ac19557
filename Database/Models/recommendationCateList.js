"use strict";
const { Model } = require("sequelize");

module.exports = (sequelize, DataTypes) => {
  const recommendationCateList = sequelize.define(
    "recommendationCateList",
    {
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      recommendationId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      type: {
        type: DataTypes.ENUM("Tab", "Exercise", "SubCategory"),
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "recommendationCateList",
      tableName: "recommendationCateList",
      timestamps: true,
      paranoid: true,
    }
  );

  recommendationCateList.associate = function (models) {
    recommendationCateList.belongsTo(models.recommendation, {
      foreignKey: "recommendationId",
      as: "recommendation",
    });
  };

  return recommendationCateList;
};
