"use strict";
module.exports = (sequelize, DataTypes) => {
  const adminToken = sequelize.define(
    "adminToken",
    {
      accessToken: DataTypes.STRING,
      adminId: DataTypes.INTEGER,
      deviceToken: DataTypes.STRING,
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      paranoid: true,
    }
  );
  adminToken.associate = function (models) {
    // associations can be defined here
    adminToken.belongsTo(models.admin, {
      foreignKey: "adminId",
      targetKey: "id",
      onDelete: "CASCADE",
    });
  };
  return adminToken;
};
