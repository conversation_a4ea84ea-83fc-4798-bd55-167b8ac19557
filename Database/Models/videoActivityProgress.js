'use strict';
module.exports = (sequelize, DataTypes) => {
    const videoActivityProgress = sequelize.define('videoActivityProgress', {
        type: {
            type: DataTypes.ENUM('breathwork', 'athleticBreathwork'),
            allowNull: false,
        },
        userId: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        isPlay: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
        }
    }, {
        tableName: 'videoActivityProgress',
        timestamps: true,
    });

    videoActivityProgress.associate = function (models) {
        videoActivityProgress.belongsTo(models.user, {
            foreignKey: 'userId',
            as: 'user',
        });
    };

    return videoActivityProgress;
};
