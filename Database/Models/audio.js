"use strict";

const { S3_AUDIO_FOLDER, S3_URL, S3_PROJECT_FOLDER } = require("../../Config/constants");

module.exports = (sequelize, DataTypes) => {
    const audio = sequelize.define(
        "audio",
        {
            title: DataTypes.STRING,
            file: {
                type: DataTypes.TEXT,
                get() {
                    const rawValue = this.getDataValue('file');
                    return !(rawValue == "null" || rawValue == "Null") ? S3_URL + S3_PROJECT_FOLDER + '/' + S3_AUDIO_FOLDER + '/' + rawValue : null;
                }
            },
            musicPreference: DataTypes.STRING,
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
            },
            updatedAt: {
                type: DataTypes.DATE,
            },
            deletedAt: {
                type: DataTypes.DATE,
                allowNull: true,
            },
        },
        {
            timestamps: true,
            paranoid: true,
        }
    );
    audio.associate = function (models) {
        // associations can be defined here
    };
    return audio;
};
