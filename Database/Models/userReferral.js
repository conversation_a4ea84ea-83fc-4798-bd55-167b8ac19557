"use strict";

const {
  S3_PROJECT_FOLDER,
  S3_REFERRAL_FOLDER,
  S3_URL,
} = require("../../Config/constants");

module.exports = (sequelize, DataTypes) => {
  const userReferral = sequelize.define(
    "userReferral",
    {
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      amount: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: "Date in YYYY-MM-DD format for referral entry",
      },
      image: {
        type: DataTypes.STRING,
        allowNull: true,
        get() {
          const rawValue = this.getDataValue("image");
          return !(rawValue == "null" || rawValue == "Null")
            ? S3_URL +
                S3_PROJECT_FOLDER +
                "/" +
                S3_REFERRAL_FOLDER +
                "/" +
                rawValue
            : null;
        },
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        defaultValue: null,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      tableName: "user_referrals",
      timestamps: true,
      paranoid: true,
    }
  );

  userReferral.associate = function (models) {
    userReferral.belongsTo(models.user, {
      foreignKey: "user_id",
      as: "user",
    });
  };

  return userReferral;
};
