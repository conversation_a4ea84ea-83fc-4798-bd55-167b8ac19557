"use strict";

const { S3_VIDEO_FOLDER, S3_URL, S3_CATEGORY_FOLDER, S3_PROJECT_FOLDER } = require("../../Config/constants");

module.exports = (sequelize, DataTypes) => {
    const playlist = sequelize.define(
        "playlist",
        {
            title: DataTypes.STRING,
            categoryId: DataTypes.INTEGER,
            description: DataTypes.TEXT,
            file: {
                type: DataTypes.TEXT,
                get() {
                    const rawValue = this.getDataValue('file');
                    return rawValue ? S3_URL + S3_PROJECT_FOLDER + '/' + S3_VIDEO_FOLDER + '/' + rawValue : null;
                }
            },
            banner: {
                type: DataTypes.TEXT,
                get() {
                    const rawValue = this.getDataValue('banner');
                    return rawValue ? S3_URL + S3_PROJECT_FOLDER + '/' + S3_CATEGORY_FOLDER + '/' + rawValue : null;
                }
            },
            benefit: DataTypes.TEXT,
            practice: DataTypes.TEXT,
            duration: DataTypes.TEXT,
            videoStatus: {
                type: DataTypes.STRING,
                allowNull: true,
                defaultValue: null,
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
            },
            updatedAt: {
                type: DataTypes.DATE,
            },
            deletedAt: {
                type: DataTypes.DATE,
                allowNull: true,
            },
        },
        {
            timestamps: true,
            // paranoid: true,
        }
    );
    playlist.associate = function (models) {
        // associations can be defined here
        playlist.belongsTo(models.category, { foreignKey: 'categoryId' });
    };
    return playlist;
};
