"use strict";
module.exports = (sequelize, DataTypes) => {
    const breathWorkProgress = sequelize.define(
        "breathWorkProgress",
        {
            userId: DataTypes.INTEGER,
            duration: {
                type: DataTypes.TIME,
                allowNull: false
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
            },
            updatedAt: {
                type: DataTypes.DATE,
                defaultValue: null,
            },
            deletedAt: {
                type: DataTypes.DATE,
                allowNull: true,
            },
        },
        {
            timestamps: true,
            paranoid: true,
        }
    );
    breathWorkProgress.associate = function (models) {
        // associations can be defined here
    };
    return breathWorkProgress;
};
