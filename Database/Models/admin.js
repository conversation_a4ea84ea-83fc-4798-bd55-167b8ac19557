"use strict";
const bcrypt = require("bcrypt");
const { S3_URL, S3_USER_FOLDER, S3_PROJECT_FOLDER } = require("../../Config/constants");

module.exports = (sequelize, DataTypes) => {
  const admin = sequelize.define(
    "admin",
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      fullName: {
        type: DataTypes.STRING,
      },
      email: {
        type: DataTypes.STRING,
      },
      password: {
        type: DataTypes.STRING,
      },
      profileImage: {
        type: DataTypes.STRING,
        get() {
          const rawValue = this.getDataValue('profileImage');
          return rawValue ? S3_URL + S3_PROJECT_FOLDER + '/' + S3_USER_FOLDER + '/' + rawValue : null;
        }
      },
      otp: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      otpUpdatedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        default: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      hooks: {
        beforeCreate: async function (user) {
          if (user.password) {
            try {
              const hashedPassword = await bcrypt.hash(user.password, 10);
              user.password = hashedPassword;
            } catch (error) {
              throw new Error(error);
            }
          }
        },
        beforeBulkUpdate: async function (user) {
          if (user.attributes.password) {
            try {
              const hashedPassword = await bcrypt.hash(
                user.attributes.password,
                10
              );
              user.attributes.password = hashedPassword;
            } catch (error) {
              throw new Error(error);
            }
          }
        },
      },
      timestamps: true,
      paranoid: true,
    }
  );
  admin.associate = function (models) {
    admin.hasMany(models.adminToken, { foreignKey: "adminId" });
  };
  admin.prototype.validatePassword = function (password) {
    try {
      return bcrypt.compareSync(password + "", this.password);
    } catch (err) {
      throw new Error("Password validation failed");
    }
  };
  return admin;
};
