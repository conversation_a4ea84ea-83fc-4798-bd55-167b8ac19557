module.exports = (sequelize, DataTypes) => {
    const contactUs = sequelize.define(
        "contactUs",
        {
            userName: DataTypes.STRING,
            email: DataTypes.STRING,
            description: DataTypes.TEXT,
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
            },
            updatedAt: {
                type: DataTypes.DATE,
            },
            deletedAt: {
                type: DataTypes.DATE,
                allowNull: true,
            },
        },
        {
            timestamps: true,
            paranoid: true,
        }
    );
    contactUs.associate = function (models) {

    };
    return contactUs;
}