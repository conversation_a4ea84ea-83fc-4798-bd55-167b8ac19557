{"STATUS": {"1XX_INFORMATIONAL": "", "CONTINUE": 100, "SWITCHING_PROTOCOLS": 101, "PROCESSING": 102, "EARLY_HINTS": 103, "2XX_SUCCESS": "", "SUCCESS": "Success.", "CREATED": "Created Successfully.", "ACCEPTED": 202, "NON_AUTHORITATIVE_INFORMATION": 203, "NO_CONTENT": 204, "RESET_CONTENT": 205, "PARTIAL_CONTENT": 206, "MULTI_STATUS": 207, "ALREADY_REPORTED": 208, "IM_USED": 226, "3XX_REDIRECTION": "", "MULTIPLE_CHOICES": 300, "MOVED_PERMANENTLY": 301, "FOUND": 302, "SEE_OTHER": 303, "NOT_MODIFIED": 304, "USE_PROXY": 305, "TEMPORARY_REDIRECT": 307, "PERMANENT_REDIRECT": 308, "4XX_CLIENT_ERROR": "", "BAD_REQUEST": 400, "UNAUTHORIZED": "You are not authorized to access this location.", "PAYMENT_REQUIRED": 402, "FORBIDDEN": "Forbidden Error.", "NOT_FOUND": "Requested resource not found.", "NOT_ALLOWED": "Method is not allowed.", "NOT_ACCEPTABLE": 406, "PROXY_AUTHENTICATION_REQUIRED": 407, "REQUEST_TIMEOUT": 408, "CONFLICT": "Provided information already exist.", "GONE": 410, "LENGTH_REQUIRED": 411, "PRECONDITION_FAILED": "Please complete other steps first.", "PAYLOAD_TOO_LARGE": 413, "URI_TOO_LONG": 414, "UNSUPPORTED_MEDIA_TYPE": 415, "RANGE_NOT_SATISFIABLE": 416, "EXPECTATION_FAILED": 417, "UNPROCESSABLE_ENTITY": 422, "VALIDATION_ERROR": "Validation error.", "NOT_VALID_DATA": "Data is understood, but is still not valid. !", "LOCKED": 423, "FAILED_DEPENDENCY": 424, "UNORDERED_COLLECTION": 425, "UPGRADE_REQUIRED": 426, "PRECONDITION_REQUIRED": 428, "TOO_MANY_REQUESTS": 429, "REQUEST_HEADER_FIELDS_TOO_LARGE": 431, "UNAVAILABLE_FOR_LEGAL_REASONS": 451, "5XX_SERVER_ERROR": "", "SERVER_ERROR": "Internal Server Error ! Please try again later.", "NOT_IMPLEMENTED": 501, "BAD_GATEWAY": 502, "SERVICE_UNAVAILABLE": 503, "GATEWAY_TIMEOUT": 504, "HTTP_VERSION_NOT_SUPPORTED": 505, "VARIANT_ALSO_NEGOTIATES": 506, "INSUFFICIENT_STORAGE": 507, "LOOP_DETECTED": 508, "BANDWIDTH_LIMIT_EXCEEDED": 509, "NOT_EXTENDED": 510, "NETWORK_AUTHENTICATION_REQUIRED": 511, "INTERNAL_ERROR": "Internal Server Error."}, "VALIDATION": {"EXISTS": {"USER": "User already exist.", "EMAIL": "Email is already registered."}, "NOT_FOUND": {"USER": "We can't find request User.", "EMAIL": "We can't find this email address.", "ACCOUNT": "We can't find this account."}, "PASSWORD": {"MISMATCH": "The provided passwords do not match. Please try again.", "TOO_SIMPLE": "Your password is too simple. Please create a more secure and complex password.", "INCORRECT": "The password you entered is incorrect. Please try again.", "VALID": "Your password meets all the required validation criteria.", "REQUIRED": "A password is required. Please provide one."}, "TOKEN": {"REQUIRED": "Token is required.", "STRING": "Token must be string.", "INVALID": "<PERSON><PERSON><PERSON>."}, "SORT_ORDER": {"ONE_OF": "Sort order key either have 'asc' or 'desc'.", "REQUIRED": "Sort order key is required.", "TYPE_ERROR": "Sort order key must be string."}, "PAGE_NAME": {"REQUIRED": "Name is required."}, "PAGE_CONTENT": {"REQUIRED": "Content is required."}, "ID": {"REQUIRED": "Id is required.", "STRING": "Id must be string."}, "EMAIL": {"INVALID": "<PERSON><PERSON> is invalid.", "REQUIRED": "Email is required."}, "OTP": {"REQUIRED": "OTP is required.", "VALID": "OTP must be only digits.", "LENGTH": "OTP must be exactly 4 digits"}, "MOBILE": {"REQUIRED": "Mobile number is required. Please provide your contact number."}, "LOGIN_TYPE": {"REQUIRED": "Login type is required. Please select a valid login method."}, "STATUS": {"REQUIRED": "Status is required."}, "MOBILENUMBER": {"REQUIRED": "Mobile number is required."}, "USERID": {"REQUIRED": "User ID is required."}, "ISPLAYLIST": {"REQUIRED": "Please specify if it is a playlist."}, "CATEGORY_ID": {"REQUIRED": "Category ID is required."}, "HAVE_SUB_CATEGORY": {"REQUIRED": "Please indicate if there are subcategories."}, "PARENTID": {"TYPE_ERROR": "Parent ID must be a valid type."}, "ORGANIZER": {"FILE_SIZE": {"LIMIT_EXCEEDED": "The uploaded file exceeds the allowed size limit. Please upload a smaller file."}}, "HOME_PAGE_IMAGE": {"REQUIRED": "Home page image is required."}, "ISTERM": {"REQUIRED": "Terms and conditions acceptance is required.", "TYPE_ERROR": "Terms agreement must be a boolean."}, "LASTNAME": {"VALID": "Last name contains invalid characters.", "TYPE_ERROR": "Last name must be a string.", "REQUIRED": "Last name is required."}, "FIRSTNAME": {"VALID": "First name contains invalid characters.", "TYPE_ERROR": "First name must be a string.", "REQUIRED": "First name is required."}, "NEW_PASSWORD": {"REQUIRED": "New password is required."}, "AUDIO": {"INVALID_SELECTION": "Invalid audio selection.", "REQUIRED": "Audio Preference is required."}, "MUSIC": {"REQUIRED": "Music Preference is required.", "TYPE_ERROR": "Music Preference must be a string."}, "CATEGORY": {"REQUIRED": "Category is required."}, "PROGRESS_DURATION": {"REQUIRED": "Progress duration is required."}, "BENIFIT": {"TYPE_ERROR": "Benefit must be string."}, "PRACTICE": {"TYPE_ERROR": "Practice must be string."}, "DURATION": {"REQUIRED": "Duration is required.", "TYPE_ERROR": "Duration must be string."}, "PAGE_TYPE": {"REQUIRED": "Page type is required."}, "DESCRIPTION": {"REQUIRED": "Description is required.", "TYPE_ERROR": "Description must be string."}, "USERNAME": {"REQUIRED": "Username is required.", "TYPE_ERROR": "Username must be string."}, "PLAYLIST_TITLE": {"VALID": "Please provide a valid playlist title."}, "EXHALETIME": {"TYPE_ERROR": "Please provide valid type."}, "CATEGORY_NAME": {"VALID": "Please provide valid category name."}, "END_DATE": {"REQUIRED": "VALIDATION.END_DATE.REQUIRED", "BEFORE_START_DATE": "VALIDATION.END_DATE.BEFORE_START_DATE"}, "AIM": {"REQUIRED": "Aim is required.", "TYPE_ERROR": "Aim must be string."}, "TYPE": {"REQUIRED": "Type is required and must be either 'breathwork' or 'athleticBreathwork'."}, "IS_PLAY": {"REQUIRED": "isPlay is required."}, "AMOUNT": {"REQUIRED": "Amount is required."}, "USER_ID": {"REQUIRED": "User ID is required."}, "SORT_BY": {"REQUIRED": "Sort by is required."}, "PAGE": {"REQUIRED": "Page is required."}, "PURCHASE_TOKEN": {"REQUIRED": "Purchase token is required."}, "DATE": {"REQUIRED": "Date is required.", "FORMAT_ERROR": "Date must be in YYYY-MM-DD format.", "TYPE_ERROR": "Date must be a string."}}, "PROCESS": {"EMAIL_SENT": "We have sent email to your account.", "EMAIL_SENT_ACCOUNT": "We have sent email to %s."}, "TOKEN": {"REQUIRED": "Token is required", "NOT_MATCHED": "<PERSON><PERSON><PERSON>."}, "USER": {"UNAUTHORIZED_USER": "You are unauthorized.", "AUTH": {"ALREADY_EXISTS": "Account already exists with this email address.", "USER_NOT_VERIFIED": "Your account is not verified. Kindly verified it first.", "LOGGED_IN": "You have been logged in successfully.", "PASSWORD_NOT_MATCH": "Password does not match.", "ACCOUNT_VERIFIED": "Account verified successfully.", "PROFILE_GET": "User profile fetched successfully.", "OTP_SENT_SUCCESSFULLY": "OTP has been sent to your email please check and verify your account.", "PASSWORD_CHANGED": "Password changed successfully.", "FORGOT_PASSWORD_MAIL_SENT": "One time otp has been sent to your email successfully.", "OTP_MISMATCH": "Invalid OTP.", "OTP_VERIFIED": "OTP verified successfully.", "NO_DETAILS_FOUND": "No details found for the user.", "REGISTER_SUCCESSFULLY": "User registered successfully.", "USER_NOT_FOUND": "User not found with this credentials.", "NOT_FOUND": "User not found with this credentials.", "OLD_PASSWORD_NOT_MATCH": "The old password you entered does not match our records.", "NEW_PASSWORD_CANNOT_BE_SAME_AS_OLD": "The new password cannot be the same as your old password. Please choose a different password.", "INACTIVE": "The user account is inactive.", "LOGGED_OUT": "User logout successfully.", "TOKEN_FOUND": "<PERSON><PERSON> found.", "DELETED_SUCCESSFULLY": "User deleted successfully.", "SUBSCRIBED_SUCCESSFULLY": "User subscribed successfully.", "SUBSCRIPTION_NOT_FOUND": "Subscription not found.", "SUBSCRIPTION_CANCELLED": "User subscription cancelled successfully.", "INVALID_REFERRAL_CODE": "Invalid referral code."}, "PROFILE": {"COMPLETED_SUCCESSFULLY": "User profile completed succesfully.", "UPDATED_SUCCESSFULLY": "User profile updated succesfully."}, "STATUS": {"UPDATED": "User status updated succesfully."}, "AUDIO_PREFERENCE": {"UPDATED_SUCCESSFULLY": "Your audio preferences have been updated successfully."}, "AIM": {"UPDATED_SUCCESSFULLY": "User Aim updated successfully."}, "CONTACT_US": {"MAIL_SENT_SUCCESSFULLY": "Contact us reply sent succesfully.", "DELETED_SUCCESSFULLY": "Contact us deleted successfully.", "NOT_FOUND": "Contact us not found."}, "REFERRAL": {"INFO_FETCHED": "Referral information fetched successfully.", "HISTORY_FETCHED": "Referral history fetched successfully."}}, "CMS": {"NO_DATA_FOUND": "No data found.", "AUTH": {"LOGGED_IN": "You have been logged in successfully.", "PASSWORD_NOT_MATCH": "Password does not match.", "USER_NOT_FOUND": "User not found with this credentials.", "INACTIVE": "Your account is inactive.", "INSUFFICIENT_PARAMETER": "Please provide sufficient parameter.", "EMAIL_NOT_EXIST": "Email does not exist.", "FORGOT_PASSWORD_MAIL_SENT": "<PERSON><PERSON> sent successfully.", "PASSWORD_RESET_SUCCESS": "Your password has been reset succesfully.", "SOMETHING_WRONG": "Something went wrong.", "DATA_SUCCESS": "Data fetched succesfully.", "LOGGED_OUT": "You have been logged out successfully.", "PROFILE_UPDATE_SUCCESS": "Your profile has been updated successfully.", "PASSWORD_SAME": "The new password cannot be the same as the old password.", "ADMIN_FORGOT_PASSWORD_MAIL_SENT": "Reset Password mail sent successfully."}, "PAGE": {"LIST_FETCHED": "Page list fetched successfully.", "DETAILS_FETCHED": "Page details fetched successfully.", "DETAILS_UPDATED": "Page details updated successfully.", "DELETED": "<PERSON> deleted successfully.", "NOT_FOUND": "Page not found.", "CONTACT_US": "Thanks, Your query has been submitted.", "DELETED_SUCCESSFULLY": "The page has been deleted successfully."}, "USER": {"NOT_FOUND": "User data not Found.", "LIST_FETCHED": "User data fetched successfully.", "DETAILS_FETCHED": "User details fetched successfully.", "DETAILS_UPDATED": "User detail updated successfully.", "CREATED_SUCCESSFULLY": "The user has been created successfully.", "DELETED_SUCCESSFULLY": "User deleted successfully."}, "STATIC_PAGE": {"ADDED_SUCCESSFULLY": "Static page added successfully.", "ALREADY_EXIST": "Static page already exist."}, "CATEGORY": {"ADDED_SUCCESSFULLY": "Category added successfully.", "ALREADY_EXIST": "Category already exists.", "LIST_FETCHED": "Category list fetched successfully.", "DETAILS_FETCHED": "Category details fetched successfully.", "NO_DATA_FOUND": "Category not found.", "UPDATED_SUCCESSFULLY": "Category updated successfully.", "DELETED_SUCCESSFULLY": "Category deleted successfully.", "NOT_FOUND": "Category not found.", "RECOMMENDATION_DETAILS_FETCHED": "Aiming details fetched successfully.", "RECOMMENDATION_ADDED": "Aiming details added successfully."}, "PLAYLIST": {"ADDED_SUCCESSFULLY": "Playlist added successfully.", "NOT_FOUND": "Playlist not found.", "DELETED_SUCCESSFULLY": "Playlist deleted successfully.", "UPDATED_SUCCESSFULLY": "Playlist updated successfully."}, "BANNER": {"LIST_FETCHED": "Banner list fetched successfully."}, "IMAGE": {"UPLOADED_SUCCESSFULLY": "Image uploaded successfully."}, "AUDIO": {"LIST_FETCHED": "Music list fetched successfully.", "ADDED_SUCCESSFULLY": "Music added successfully.", "DETAILS_FETCHED": "Music details fetched successfully.", "DELETED_SUCCESSFULLY": "Music deleted successfully.", "NOT_FOUND": "Music not found.", "UPDATED_SUCCESSFULLY": "Audio updated successfully."}, "CONTACT_US": {"LIST_FETCHED": "Contact us list fetched successfully.", "DETAILS_FETCHED": "Contact us details fetched successfully."}, "RECOMMENDATION": {"NOT_FOUND": "Aiming Recommendation not found."}, "CATEGORY_RECOMMENDATION": {"NO_DATA_FOUND": "Aiming Data not found.", "LIST_FETCHED": "Aiming List fetched successfully."}, "STATS": {"LIST_FETCHED": "Dashboard data fetched successfully"}, "USER_STAT": {"FETCHED": "User statistics fetched successfully."}, "BREATHWORK_PROGRESS_STAT": {"FETCHED": "Breathwork progress statistics fetched successfully."}, "USER_REFERRAL": {"ADDED_SUCCESSFULLY": "User referral added successfully.", "ALREADY_ADDED_THIS_MONTH": "User has already added a referral this month.", "LIST_FETCHED": "User referral list fetched successfully.", "ALREADY_ADDED_FOR_THIS_MONTH": "User has already added a referral for this month."}, "SUBSCRIPTION_STAT": {"FETCHED": "Subscription statistics fetched successfully"}}, "GENERAL": {"SOMETHING_WRONG": "Something went wrong."}, "HOME": {"DETAILS_FETCHED_SUCCESSFULLY": "Home details have been fetched successfully."}, "APP": {"CATEGORY": {"NOT_FOUND": "The category not found with this name.", "FETCHED_SUCCESSFULLY": "The app category has been fetched successfully.", "VIDEO_NOT_FOUND": "No video found for the app category.", "FETCHED_SUCCESSFYLLY": "Category fetched successfully.", "AIM_LIST_FETCHED_SUCCESSFYLLY": "Aim list has been fetched successfully.", "AIM_NOT_FOUND": "No aiming category was found.", "VIDEO_ACTIVITY_ADDED": "Video activity progress has been added successfully."}, "PROGRESS": {"ADDED_SUCCESSFULLY": "Progress has been added successfully.", "FETCHED_SUCCESSFULLY": "Progress has been fetched successfully."}, "STATIC_PAGE": {"FETCHED_SUCCESSFULLY": "Static page fetched successfully.", "NOT_FOUND": "Static page not found.", "FETCHED_SUCCESSULLY": "Static page fetched successfully."}, "CONTACT_US": {"ADDED_SUCCESSFULLY": "Contact us message added successfully."}, "AUDIO": {"FETCHED_SUCCESSFULLY": "Music fetched successfully."}, "PLAYLIST": {"FETCHED_SUCCESSFULLY": "Playlist fetched successfully.", "FETCHED_SUCCESSFYLLY": "Playlist fetched successfully."}, "USER_REFERRAL": {"LIST_FETCHED": "User referral list fetched successfully.", "STATS_FETCHED": "User referral statistics fetched successfully.", "LATEST_FETCHED": "Latest user referrals fetched successfully."}}, "query": {"id must be a `number` type, but the final value was": " `NaN` (cast from the value `\"test\"`)."}, "branch is not a function": "branch is not a function.", "Recommendations separated successfully": "Recommendations separated successfully.", "The user account is inactive.": "The user account is inactive."}